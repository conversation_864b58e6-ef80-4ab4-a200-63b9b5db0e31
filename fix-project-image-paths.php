<?php
// Define access constant and bypass security checks
define('MONOLITH_ACCESS', true);
session_start();
$_SESSION['admin_logged_in'] = true;

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/functions.php';

echo "<h1>FIX PROJECT IMAGE PATHS</h1><pre>";

try {
    $db = Database::getConnection();
    
    echo "=== FIXING PROJECT IMAGE PATHS ===\n\n";
    
    // Get all projects with image paths that need fixing
    $stmt = $db->query("SELECT id, title, featured_image, gallery FROM projects WHERE featured_image IS NOT NULL OR gallery IS NOT NULL");
    $projects = $stmt->fetchAll();
    
    $fixed_count = 0;
    
    foreach ($projects as $project) {
        $needs_update = false;
        $new_featured_image = $project['featured_image'];
        $new_gallery = $project['gallery'];
        
        echo "--- Project: {$project['title']} ---\n";
        
        // Fix featured image path
        if ($project['featured_image']) {
            echo "Current featured image: {$project['featured_image']}\n";
            
            // Check if it has the wrong /admin/ path
            if (strpos($project['featured_image'], '/admin/assets/images/uploads/') !== false) {
                $new_featured_image = str_replace('/admin/assets/images/uploads/', '/assets/images/uploads/', $project['featured_image']);
                echo "Fixed featured image: $new_featured_image\n";
                $needs_update = true;
            }
            // Check if it's a relative path that needs to be absolute
            elseif (strpos($project['featured_image'], 'assets/images/uploads/') === 0) {
                $new_featured_image = SITE_URL . '/' . $project['featured_image'];
                echo "Made absolute: $new_featured_image\n";
                $needs_update = true;
            }
            // Check if it's an external URL that should be local
            elseif (strpos($project['featured_image'], 'cdn.prod.website-files.com') !== false) {
                echo "External URL detected - keeping as is (manual replacement needed)\n";
            }
        }
        
        // Fix gallery images
        if ($project['gallery']) {
            $gallery = json_decode($project['gallery'], true);
            if ($gallery) {
                $gallery_fixed = false;
                foreach ($gallery as &$img) {
                    $img_url = is_array($img) ? $img['url'] : $img;
                    $original_url = $img_url;
                    
                    // Fix /admin/ path issue
                    if (strpos($img_url, '/admin/assets/images/uploads/') !== false) {
                        $img_url = str_replace('/admin/assets/images/uploads/', '/assets/images/uploads/', $img_url);
                        $gallery_fixed = true;
                    }
                    // Make relative paths absolute
                    elseif (strpos($img_url, 'assets/images/uploads/') === 0) {
                        $img_url = SITE_URL . '/' . $img_url;
                        $gallery_fixed = true;
                    }
                    
                    if (is_array($img)) {
                        $img['url'] = $img_url;
                    } else {
                        $img = $img_url;
                    }
                    
                    if ($original_url !== $img_url) {
                        echo "Fixed gallery image: $original_url → $img_url\n";
                    }
                }
                
                if ($gallery_fixed) {
                    $new_gallery = json_encode($gallery);
                    $needs_update = true;
                }
            }
        }
        
        // Update database if needed
        if ($needs_update) {
            $update_sql = "UPDATE projects SET featured_image = ?, gallery = ? WHERE id = ?";
            $stmt = $db->prepare($update_sql);
            if ($stmt->execute([$new_featured_image, $new_gallery, $project['id']])) {
                echo "✅ Database updated successfully\n";
                $fixed_count++;
            } else {
                echo "❌ Failed to update database\n";
            }
        } else {
            echo "No changes needed\n";
        }
        
        echo "\n";
    }
    
    echo "=== SUMMARY ===\n";
    echo "Projects processed: " . count($projects) . "\n";
    echo "Projects fixed: $fixed_count\n";
    
    echo "\n=== VERIFICATION ===\n";
    // Re-check the database
    $stmt = $db->query("SELECT id, title, featured_image FROM projects WHERE featured_image IS NOT NULL");
    $updated_projects = $stmt->fetchAll();
    
    foreach ($updated_projects as $project) {
        echo "Project: {$project['title']}\n";
        echo "Image: {$project['featured_image']}\n";
        
        // Check if file exists
        if ($project['featured_image']) {
            $file_path = '';
            if (strpos($project['featured_image'], SITE_URL) === 0) {
                $file_path = str_replace(SITE_URL . '/', '', $project['featured_image']);
            }
            
            if ($file_path && file_exists($file_path)) {
                echo "✅ File exists\n";
            } elseif ($file_path) {
                echo "❌ File missing: $file_path\n";
            } else {
                echo "ℹ️ External URL\n";
            }
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>

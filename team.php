<?php
/**
 * Team Page - Meet Our Expert Team
 * Features CEO details and team members
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

$pageTitle = 'Our Team - Meet the Experts Behind Monolith Design';
$pageDescription = 'Meet our talented team of architects, engineers, and designers who bring decades of experience to every project.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Team Page Specific CSS -->
    <style>
        /* ===== TEAM PAGE STYLES ===== */
        
        /* CEO Spotlight Section */
        .ceo-spotlight {
            padding: 8rem 0;
            background: #f8f9fa;
        }
        
        .ceo-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6rem;
            align-items: center;
        }
        
        .ceo-image {
            position: relative;
        }
        
        .ceo-image img {
            width: 100%;
            border-radius: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .ceo-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .ceo-info h2 {
            color: var(--accent-color);
            font-size: 2.8rem;
            margin-bottom: 1rem;
        }
        
        .ceo-title {
            font-size: 1.5rem;
            color: #666;
            margin-bottom: 2rem;
            font-weight: 300;
        }
        
        .ceo-bio {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .ceo-achievements {
            list-style: none;
            padding: 0;
        }
        
        .ceo-achievements li {
            padding: 0.5rem 0;
            padding-left: 2rem;
            position: relative;
            color: #555;
        }
        
        .ceo-achievements li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }
        
        /* Team Grid */
        .team-grid {
            padding: 8rem 0;
        }
        
        .team-grid h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 4rem;
            color: var(--text-color);
        }
        
        .team-members {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
        }
        
        .team-member {
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        
        .team-member:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }
        
        .team-member-image {
            height: 280px;
            overflow: hidden;
            position: relative;
        }
        
        .team-member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .team-member:hover .team-member-image img {
            transform: scale(1.1);
        }
        
        .team-member-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            padding: 2rem;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .team-member:hover .team-member-overlay {
            transform: translateY(0);
        }
        
        .team-member-social {
            display: flex;
            gap: 1rem;
        }
        
        .social-link {
            width: 40px;
            height: 40px;
            background: var(--accent-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
        }
        
        .social-link:hover {
            transform: scale(1.1);
        }
        
        .team-member-info {
            padding: 2rem;
        }
        
        .team-member-name {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }
        
        .team-member-role {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .team-member-bio {
            color: #666;
            line-height: 1.6;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .ceo-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }
            

            
            .ceo-info h2 {
                font-size: 2.2rem;
            }
        }
        
        @media (max-width: 768px) {

            
            .ceo-spotlight,
            .team-grid {
                padding: 4rem 0;
            }
            
            .team-members {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Page Hero Section -->
    <?php
    // Hero data for team page
    $hero_title = 'Our Team';
    $hero_subtitle = 'Meet the Experts Behind Monolith Design';
    $hero_background = 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=1600&h=900&fit=crop'; // Team/office background
    $hero_overlay_class = 'hero-faded-overlay';
    $breadcrumb_items = [
        ['title' => 'Home', 'url' => 'index.php'],
        ['title' => 'Team', 'url' => '']
    ];

    // Load hero template
    include 'templates/page-hero.php';
    ?>

    <!-- CEO Spotlight -->
    <section class="ceo-spotlight">
        <div class="container">
            <div class="ceo-content">
                <div class="ceo-image">
                    <img src="<?php echo themeUrl('images/demo-images/ceo-portrait.jpg'); ?>" alt="CEO Portrait" onerror="this.src='https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop&crop=face'">
                </div>
                <div class="ceo-info">
                    <h2>Alexander Thompson</h2>
                    <div class="ceo-title">Chief Executive Officer & Principal Architect</div>
                    <div class="ceo-bio">
                        <p>With over 25 years of experience in architectural design and construction management, Alexander founded Monolith Design with a vision to create spaces that inspire and endure. His expertise spans commercial, residential, and institutional projects, with a particular focus on sustainable design and innovative construction methods.</p>
                        
                        <p>Alexander holds a Master's degree in Architecture from MIT and is a licensed architect in multiple states. He has been recognized by the American Institute of Architects for his contributions to sustainable design and has spoken at numerous international conferences on the future of architecture.</p>
                    </div>
                    
                    <ul class="ceo-achievements">
                        <li>Licensed Architect in 12 states</li>
                        <li>LEED AP BD+C Certified Professional</li>
                        <li>AIA Gold Medal Recipient (2023)</li>
                        <li>Featured in Architectural Digest Top 100 Architects</li>
                        <li>Over $2B in completed project value</li>
                        <li>TEDx Speaker on Sustainable Architecture</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Members Grid -->
    <section class="team-grid">
        <div class="container">
            <h2>Our Professional Team</h2>
            <div class="team-members">
                <!-- Team Member 1 -->
                <div class="team-member">
                    <div class="team-member-image">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b643?w=400&h=500&fit=crop&crop=face" alt="Sarah Chen">
                        <div class="team-member-overlay">
                            <div class="team-member-social">
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="team-member-info">
                        <h3 class="team-member-name">Sarah Chen</h3>
                        <div class="team-member-role">Senior Architect</div>
                        <p class="team-member-bio">Specializing in sustainable residential design with 12 years of experience. Sarah leads our green building initiatives and LEED certification projects.</p>
                    </div>
                </div>

                <!-- Team Member 2 -->
                <div class="team-member">
                    <div class="team-member-image">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=500&fit=crop&crop=face" alt="Marcus Rodriguez">
                        <div class="team-member-overlay">
                            <div class="team-member-social">
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="team-member-info">
                        <h3 class="team-member-name">Marcus Rodriguez</h3>
                        <div class="team-member-role">Structural Engineer</div>
                        <p class="team-member-bio">Expert in complex structural systems and seismic design. Marcus ensures our buildings are not only beautiful but also safe and structurally sound.</p>
                    </div>
                </div>

                <!-- Team Member 3 -->
                <div class="team-member">
                    <div class="team-member-image">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=500&fit=crop&crop=face" alt="Emily Foster">
                        <div class="team-member-overlay">
                            <div class="team-member-social">
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="team-member-info">
                        <h3 class="team-member-name">Emily Foster</h3>
                        <div class="team-member-role">Interior Design Lead</div>
                        <p class="team-member-bio">Passionate about creating functional and beautiful interior spaces. Emily brings creativity and attention to detail to every project's interior design.</p>
                    </div>
                </div>

                <!-- Team Member 4 -->
                <div class="team-member">
                    <div class="team-member-image">
                        <img src="https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=500&fit=crop&crop=face" alt="David Kim">
                        <div class="team-member-overlay">
                            <div class="team-member-social">
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="team-member-info">
                        <h3 class="team-member-name">David Kim</h3>
                        <div class="team-member-role">Project Manager</div>
                        <p class="team-member-bio">Ensuring projects are delivered on time and within budget. David coordinates all aspects of construction and maintains clear communication with clients.</p>
                    </div>
                </div>

                <!-- Team Member 5 -->
                <div class="team-member">
                    <div class="team-member-image">
                        <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=500&fit=crop&crop=face" alt="Lisa Park">
                        <div class="team-member-overlay">
                            <div class="team-member-social">
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="team-member-info">
                        <h3 class="team-member-name">Lisa Park</h3>
                        <div class="team-member-role">Urban Planning Specialist</div>
                        <p class="team-member-bio">Focused on sustainable urban development and community planning. Lisa ensures our projects integrate seamlessly with their surroundings.</p>
                    </div>
                </div>

                <!-- Team Member 6 -->
                <div class="team-member">
                    <div class="team-member-image">
                        <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=500&fit=crop&crop=face" alt="James Wilson">
                        <div class="team-member-overlay">
                            <div class="team-member-social">
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-link">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="team-member-info">
                        <h3 class="team-member-name">James Wilson</h3>
                        <div class="team-member-role">Construction Manager</div>
                        <p class="team-member-bio">With 15 years in construction management, James oversees the building process from groundbreaking to final inspection, ensuring quality at every step.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>

<?php
/**
 * Reusable Hero CTA Template
 * Can be used across different pages with custom content
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Determine page name for database lookup
$current_page = isset($hero_page_name) ? $hero_page_name : 'home';

// Get hero section from database or use custom data
$db_hero = getHeroSection($current_page);

// Use custom data if provided, otherwise use database data, then fallback to defaults
$hero_caption = isset($hero_data['caption']) ? $hero_data['caption'] :
                ($db_hero ? $db_hero['caption'] : getThemeOption('hero_cta_caption', "Ready to Build?"));

$hero_title = isset($hero_data['title']) ? $hero_data['title'] :
              ($db_hero ? $db_hero['title'] : getThemeOption('hero_cta_title', 'Ready to Get Started?'));

$hero_description = isset($hero_data['description']) ? $hero_data['description'] :
                    ($db_hero ? $db_hero['description'] : getThemeOption('hero_cta_description', "Let's transform your vision into reality with our innovative architectural solutions and expert craftsmanship."));

$hero_button_text = isset($hero_data['button_text']) ? $hero_data['button_text'] :
                    ($db_hero ? $db_hero['button_text'] : getThemeOption('hero_cta_button_text', 'Start Your Project'));

$hero_button_link = isset($hero_data['button_link']) ? $hero_data['button_link'] :
                    ($db_hero ? $db_hero['button_link'] : getThemeOption('hero_cta_button_link', 'contact'));

// Handle background based on database settings or custom data
$background_style = '';
$background_class = 'cta-section';

if ($db_hero) {
    if ($db_hero['background_type'] === 'image' && !empty($db_hero['background_image'])) {
        $background_style = "background-image: url('" . ensureAbsoluteUrl($db_hero['background_image']) . "'); background-size: cover; background-position: center;";
        $background_class .= ' cta-image-bg';
    } else {
        // Use gradient background
        $gradient = $db_hero['background_gradient'] ?: 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)';
        $background_style = "background: $gradient;";
        $background_class .= ' cta-gradient-bg';
    }
} elseif (isset($hero_data['background']) && !empty($hero_data['background'])) {
    $background_style = "background-image: url('" . $hero_data['background'] . "'); background-size: cover; background-position: center;";
    $background_class .= ' cta-image-bg';
} else {
    // Default gradient
    $background_style = "background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);";
    $background_class .= ' cta-gradient-bg';
}
?>

<!-- Dynamic Hero CTA Section -->
<section class="<?php echo $background_class; ?>" style="<?php echo $background_style; ?> padding: 6rem 0; position: relative; color: white;">
    <!-- Overlay for image backgrounds -->
    <?php if (strpos($background_class, 'cta-image-bg') !== false): ?>
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%); z-index: 1;"></div>
    <?php endif; ?>

    <div class="container" style="position: relative; z-index: 2;">
        <div class="hero-content" style="text-align: center; max-width: 600px; margin: 0 auto;">
            <?php if (!empty($hero_caption)): ?>
            <div class="caption" style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 1px;">
                <?php echo htmlspecialchars($hero_caption); ?>
            </div>
            <?php endif; ?>

            <h2 class="hero-title" style="color: var(--accent-color); font-size: 2.5rem; margin-bottom: 1rem; font-weight: 700;">
                <?php echo htmlspecialchars($hero_title); ?>
            </h2>

            <?php if (!empty($hero_description)): ?>
            <p class="hero-subtitle" style="color: white; font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; line-height: 1.6;">
                <?php echo htmlspecialchars($hero_description); ?>
            </p>
            <?php endif; ?>

            <a href="<?php echo siteUrl($hero_button_link); ?>" class="btn btn-primary" style="display: inline-flex; align-items: center; gap: 0.5rem; background: var(--accent-color); color: white; padding: 1rem 2rem; border-radius: 4px; text-decoration: none; font-weight: 500; transition: all 0.3s ease;">
                <?php echo htmlspecialchars($hero_button_text); ?>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M7 17L17 7M17 7H7M17 7V17"/>
                </svg>
            </a>
        </div>
    </div>
</section>

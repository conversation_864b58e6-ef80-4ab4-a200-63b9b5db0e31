<?php
/**
 * Hero Sections Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/hero-page-detection.php';

// Session is already started in functions.php

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_hero':
                try {
                    $db = Database::getConnection();
                    
                    // Handle background image upload
                    $background_image = $_POST['current_background_image'] ?? '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    }
                    
                    $stmt = $db->prepare("
                        UPDATE hero_sections SET
                            caption = ?, title = ?, description = ?,
                            button_text = ?, button_link = ?,
                            background_type = ?, background_image = ?, background_gradient = ?,
                            height_type = ?, height_custom = ?,
                            caption_color = ?, title_color = ?, description_color = ?,
                            button_bg_color = ?, button_text_color = ?, button_hover_bg_color = ?,
                            background_color = ?, background_opacity = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");

                    $result = $stmt->execute([
                        sanitizeInput($_POST['caption']),
                        sanitizeInput($_POST['title']),
                        sanitizeInput($_POST['description']),
                        sanitizeInput($_POST['button_text']),
                        ($_POST['button_link'] === 'custom') ? sanitizeInput($_POST['custom_button_link']) : sanitizeInput($_POST['button_link']),
                        sanitizeInput($_POST['background_type']),
                        $background_image,
                        sanitizeInput($_POST['background_gradient']),
                        sanitizeInput($_POST['height_type']),
                        $_POST['height_type'] === 'custom' ? intval($_POST['height_custom']) : null,
                        sanitizeInput($_POST['caption_color']),
                        sanitizeInput($_POST['title_color']),
                        sanitizeInput($_POST['description_color']),
                        sanitizeInput($_POST['button_bg_color']),
                        sanitizeInput($_POST['button_text_color']),
                        sanitizeInput($_POST['button_hover_bg_color']),
                        sanitizeInput($_POST['background_color']),
                        floatval($_POST['background_opacity']),
                        intval($_POST['hero_id'])
                    ]);
                    
                    if ($result) {
                        $message = 'Hero section updated successfully!';
                    } else {
                        $error = 'Failed to update hero section.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;
                
            case 'toggle_active':
                try {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("UPDATE hero_sections SET active = NOT active WHERE id = ?");
                    $result = $stmt->execute([intval($_POST['hero_id'])]);
                    
                    if ($result) {
                        $message = 'Hero section status updated!';
                    } else {
                        $error = 'Failed to update status.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;

            case 'create_hero_for_page':
                try {
                    $page_name = sanitizeInput($_POST['page_name']);
                    $page_title = sanitizeInput($_POST['page_title']);

                    $hero_data = [
                        'caption' => sanitizeInput($_POST['caption']),
                        'title' => sanitizeInput($_POST['title']),
                        'description' => sanitizeInput($_POST['description']),
                        'button_text' => sanitizeInput($_POST['button_text']),
                        'button_link' => ($_POST['button_link'] === 'custom') ? sanitizeInput($_POST['custom_button_link']) : sanitizeInput($_POST['button_link']),
                        'height_type' => sanitizeInput($_POST['height_type']),
                        'caption_color' => sanitizeInput($_POST['caption_color']),
                        'title_color' => sanitizeInput($_POST['title_color']),
                        'description_color' => sanitizeInput($_POST['description_color']),
                        'button_bg_color' => sanitizeInput($_POST['button_bg_color']),
                        'button_text_color' => sanitizeInput($_POST['button_text_color']),
                        'button_hover_bg_color' => sanitizeInput($_POST['button_hover_bg_color']),
                        'background_color' => sanitizeInput($_POST['background_color']),
                        'background_opacity' => floatval($_POST['background_opacity'])
                    ];

                    if ($_POST['height_type'] === 'custom') {
                        $hero_data['height_custom'] = intval($_POST['height_custom']);
                    }

                    $result = createHeroSectionForPage($page_name, $page_title, $hero_data);

                    if ($result) {
                        $message = "Hero section created successfully for $page_title!";
                    } else {
                        $error = 'Failed to create hero section.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;

            case 'scan_pages':
                try {
                    $new_pages = updateDetectedPages();
                    if (empty($new_pages)) {
                        $message = 'No new pages detected.';
                    } else {
                        $message = 'Found ' . count($new_pages) . ' new pages!';
                    }
                } catch (Exception $e) {
                    $error = 'Error scanning pages: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all hero sections
try {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM hero_sections ORDER BY page_name");
    $hero_sections = $stmt->fetchAll();
} catch (Exception $e) {
    $error = 'Error loading hero sections: ' . $e->getMessage();
    $hero_sections = [];
}

// Get pages without hero sections for dynamic creation
try {
    $pages_without_heroes = getPagesWithoutHeroSections();
} catch (Exception $e) {
    $pages_without_heroes = [];
}

// Get hero for editing
$edit_hero = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $db->prepare("SELECT * FROM hero_sections WHERE id = ?");
        $stmt->execute([intval($_GET['edit'])]);
        $edit_hero = $stmt->fetch();
    } catch (Exception $e) {
        $error = 'Error loading hero section: ' . $e->getMessage();
    }
}

$page_title = 'Hero Sections Management';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ecf0f1;
            color: #2c3e50;
        }

        .admin-header {
            background: #34495e;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-nav {
            background: #2c3e50;
            padding: 0;
            display: flex;
            overflow-x: auto;
        }

        .admin-nav a {
            color: #bdc3c7;
            text-decoration: none;
            padding: 1rem 1.5rem;
            white-space: nowrap;
            transition: background 0.3s;
        }

        .admin-nav a:hover,
        .admin-nav a.active {
            background: #34495e;
            color: white;
        }

        .admin-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .admin-card-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #ecf0f1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-header h2 {
            color: #2c3e50;
            font-size: 1.5rem;
        }

        .admin-card-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        input[type="text"],
        input[type="email"],
        input[type="url"],
        input[type="file"],
        textarea,
        select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: #3498db;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 1rem;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: #E67E22;
        }

        .btn-primary:hover {
            background: #d35400;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .admin-table th,
        .admin-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .admin-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .admin-table tr:hover {
            background: #f8f9fa;
        }

        .message {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        /* Hero-specific styles */
        .hero-preview {
            background: #f5f5f5;
            border-radius: 8px;
            padding: 2rem;
            margin: 1rem 0;
            position: relative;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }

        .hero-preview.gradient-bg {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
        }

        .hero-preview.image-bg {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .hero-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            z-index: 1;
        }

        .hero-preview.gradient-bg::before {
            background: var(--gradient-preview, linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%));
        }

        .hero-preview.image-bg::before {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
        }

        .hero-preview-content {
            position: relative;
            z-index: 2;
        }

        .hero-preview .caption {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }

        .hero-preview .title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .hero-preview .description {
            font-size: 1rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }

        .hero-preview .button {
            display: inline-block;
            background: #E67E22;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
        }

        .background-type-toggle {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }

        .background-type-toggle label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .gradient-presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .gradient-preset {
            height: 60px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .gradient-preset:hover,
        .gradient-preset.selected {
            border-color: #E67E22;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        small {
            color: #666;
            font-size: 0.9rem;
            display: block;
            margin-top: 0.5rem;
        }

        
        .hero-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            z-index: 1;
        }
        
        .hero-preview.gradient-bg::before {
            background: var(--gradient-preview, linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%));
        }
        
        .hero-preview.image-bg::before {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
        }
        
        .hero-preview-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-preview .caption {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }
        
        .hero-preview .title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .hero-preview .description {
            font-size: 1rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }
        
        .hero-preview .button {
            display: inline-block;
            background: #E67E22;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .background-type-toggle {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .background-type-toggle label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }
        
        .gradient-presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .gradient-preset {
            height: 60px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .gradient-preset:hover,
        .gradient-preset.selected {
            border-color: #E67E22;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge.inactive {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><?php echo $page_title; ?></h1>
            <a href="../" class="btn btn-secondary" target="_blank">View Site</a>
        </div>
        
        <nav class="admin-nav">
            <a href="index.php">Theme Options</a>
            <a href="sliders.php">Sliders</a>
            <a href="hero-sections.php" class="active">Hero Sections</a>
            <a href="services.php">Services</a>
            <a href="projects.php">Projects</a>
            <a href="team.php">Team</a>
            <a href="testimonials.php">Testimonials</a>
            <a href="blog.php">Blog</a>
            <a href="contacts.php">Contact Submissions</a>
        </nav>
        
        <div class="admin-content">
            <?php if ($message): ?>
                <div class="message success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="message error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($edit_hero): ?>
                <!-- Edit Hero Section Form -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2>Edit Hero Section: <?php echo htmlspecialchars($edit_hero['page_title']); ?></h2>
                        <a href="hero-sections.php" class="btn btn-secondary">← Back to List</a>
                    </div>
                    
                    <div class="admin-card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="update_hero">
                            <input type="hidden" name="hero_id" value="<?php echo $edit_hero['id']; ?>">
                            <input type="hidden" name="current_background_image" value="<?php echo htmlspecialchars($edit_hero['background_image']); ?>">
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="caption">Caption Text</label>
                                    <input type="text" id="caption" name="caption" value="<?php echo htmlspecialchars($edit_hero['caption']); ?>" placeholder="Ready to Build?">
                                    <small>Small text above the main title</small>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="title">Main Title</label>
                                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($edit_hero['title']); ?>" required placeholder="Ready to Get Started?">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="description">Description Text</label>
                                    <textarea id="description" name="description" rows="3" placeholder="Let's transform your vision into reality..."><?php echo htmlspecialchars($edit_hero['description']); ?></textarea>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="button_text">Button Text</label>
                                    <input type="text" id="button_text" name="button_text" value="<?php echo htmlspecialchars($edit_hero['button_text']); ?>" placeholder="Start Your Project">
                                </div>
                                
                                <div class="form-group">
                                    <label for="button_link">Button Link</label>
                                    <select id="button_link" name="button_link" onchange="toggleCustomLinkInput()">
                                        <option value="">Select a page...</option>
                                        <?php
                                        // Get all pages from site_pages table
                                        $db = Database::getConnection();
                                        $pages_query = "SELECT page_name, page_title FROM site_pages ORDER BY page_title";
                                        $pages_result = $db->query($pages_query);
                                        $current_link = $edit_hero['button_link'] ?? '';
                                        $is_custom_link = true;

                                        if ($pages_result) {
                                            while ($page = $pages_result->fetch(PDO::FETCH_ASSOC)) {
                                                $selected = ($current_link === $page['page_name']) ? 'selected' : '';
                                                if ($selected) $is_custom_link = false;
                                                echo '<option value="' . htmlspecialchars($page['page_name']) . '" ' . $selected . '>' . htmlspecialchars($page['page_title']) . '</option>';
                                            }
                                        }
                                        ?>
                                        <option value="custom" <?php echo $is_custom_link && !empty($current_link) ? 'selected' : ''; ?>>Other/Custom URL</option>
                                    </select>
                                    <input type="text" id="custom_button_link" name="custom_button_link"
                                           value="<?php echo $is_custom_link ? htmlspecialchars($current_link) : ''; ?>"
                                           placeholder="Enter custom URL or page slug"
                                           style="margin-top: 10px; <?php echo $is_custom_link && !empty($current_link) ? '' : 'display: none;'; ?>">
                                    <small>Select a page or choose "Other/Custom URL" for external links</small>
                                </div>
                            </div>
                            
                            <!-- Background Type Selection -->
                            <div class="form-group">
                                <label>Background Type</label>
                                <div class="background-type-toggle">
                                    <label>
                                        <input type="radio" name="background_type" value="gradient" <?php echo $edit_hero['background_type'] === 'gradient' ? 'checked' : ''; ?> onchange="toggleBackgroundType()">
                                        Gradient Background
                                    </label>
                                    <label>
                                        <input type="radio" name="background_type" value="image" <?php echo $edit_hero['background_type'] === 'image' ? 'checked' : ''; ?> onchange="toggleBackgroundType()">
                                        Image Background
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Gradient Background Options -->
                            <div id="gradient-options" style="display: <?php echo $edit_hero['background_type'] === 'gradient' ? 'block' : 'none'; ?>;">
                                <div class="form-group">
                                    <label for="background_gradient">Background Gradient</label>
                                    <small style="display: block; margin-bottom: 10px; color: #666;">Choose from professional gradient presets or enter a custom CSS gradient</small>
                                    <input type="text" id="background_gradient" name="background_gradient" value="<?php echo htmlspecialchars($edit_hero['background_gradient']); ?>" placeholder="linear-gradient(...)">
                                    
                                    <div class="gradient-presets">
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%); color: white; border: 1px solid rgba(255,255,255,0.2);" onclick="selectGradient('linear-gradient(135deg, rgba(0, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)')">
                                            Professional Dark
                                        </div>
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(230, 126, 34, 0.85) 0%, rgba(211, 84, 0, 0.65) 100%); color: white; border: 1px solid rgba(255,255,255,0.2);" onclick="selectGradient('linear-gradient(135deg, rgba(230, 126, 34, 0.85) 0%, rgba(211, 84, 0, 0.65) 100%)')">
                                            Warm Orange
                                        </div>
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(44, 62, 80, 0.9) 0%, rgba(52, 73, 94, 0.75) 100%); color: white; border: 1px solid rgba(255,255,255,0.2);" onclick="selectGradient('linear-gradient(135deg, rgba(44, 62, 80, 0.9) 0%, rgba(52, 73, 94, 0.75) 100%)')">
                                            Corporate Blue
                                        </div>
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(46, 204, 113, 0.8) 0%, rgba(39, 174, 96, 0.6) 100%); color: white; border: 1px solid rgba(255,255,255,0.2);" onclick="selectGradient('linear-gradient(135deg, rgba(46, 204, 113, 0.8) 0%, rgba(39, 174, 96, 0.6) 100%)')">
                                            Modern Green
                                        </div>
                                        <?php 
                                        // Get current accent color and convert to rgba for gradient
                                        $accent_color = getThemeOption('accent_color', '#E67E22');
                                        // Convert hex to RGB
                                        $hex = ltrim($accent_color, '#');
                                        $r = hexdec(substr($hex, 0, 2));
                                        $g = hexdec(substr($hex, 2, 2));
                                        $b = hexdec(substr($hex, 4, 2));
                                        $gradient_strong = "linear-gradient(135deg, rgba($r, $g, $b, 0.9) 0%, rgba($r, $g, $b, 0.6) 100%)";
                                        ?>
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(155, 89, 182, 0.8) 0%, rgba(142, 68, 173, 0.6) 100%); color: white; border: 1px solid rgba(255,255,255,0.2);" onclick="selectGradient('linear-gradient(135deg, rgba(155, 89, 182, 0.8) 0%, rgba(142, 68, 173, 0.6) 100%)')">
                                            Creative Purple
                                        </div>
                                        <div class="gradient-preset" style="background: <?php echo $gradient_strong; ?>; color: white; border: 1px solid rgba(255,255,255,0.2);" onclick="selectGradient('<?php echo $gradient_strong; ?>')">
                                            Theme Accent
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Image Background Options -->
                            <div id="image-options" style="display: <?php echo $edit_hero['background_type'] === 'image' ? 'block' : 'none'; ?>;">
                                <div class="form-group">
                                    <label for="background_image">Background Image</label>
                                    <input type="file" id="background_image" name="background_image" accept="image/*">
                                    <?php if ($edit_hero['background_image']): ?>
                                        <div style="margin-top: 1rem;">
                                            <img src="<?php echo ensureAbsoluteUrl($edit_hero['background_image']); ?>" alt="Current Background" style="max-width: 200px; height: auto; border-radius: 4px;">
                                            <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Current background image</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Height Controls -->
                            <div class="form-group">
                                <label>Hero Section Height</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <select name="height_type" id="height_type" onchange="toggleCustomHeight()">
                                            <option value="small" <?php echo ($edit_hero['height_type'] ?? 'medium') === 'small' ? 'selected' : ''; ?>>Small (300px)</option>
                                            <option value="medium" <?php echo ($edit_hero['height_type'] ?? 'medium') === 'medium' ? 'selected' : ''; ?>>Medium (400px)</option>
                                            <option value="500px" <?php echo ($edit_hero['height_type'] ?? 'medium') === '500px' ? 'selected' : ''; ?>>500px</option>
                                            <option value="large" <?php echo ($edit_hero['height_type'] ?? 'medium') === 'large' ? 'selected' : ''; ?>>Large (600px)</option>
                                            <option value="custom" <?php echo ($edit_hero['height_type'] ?? 'medium') === 'custom' ? 'selected' : ''; ?>>Custom</option>
                                        </select>
                                    </div>
                                    <div class="form-group" id="custom-height-group" style="display: <?php echo ($edit_hero['height_type'] ?? 'medium') === 'custom' ? 'block' : 'none'; ?>;">
                                        <input type="number" name="height_custom" id="height_custom" value="<?php echo $edit_hero['height_custom'] ?? 400; ?>" placeholder="400" min="200" max="1000">
                                        <small>Height in pixels (200-1000)</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Text Colors -->
                            <div class="form-group">
                                <label>Text Colors</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="caption_color" style="display: inline-block; margin-right: 10px; vertical-align: middle;">Caption Color</label>
                                        <input type="color" id="caption_color" name="caption_color" value="<?php echo $edit_hero['caption_color'] ?? '#ffffff'; ?>" onchange="updatePreviewColors()" style="display: inline-block; vertical-align: middle; width: 50px; height: 35px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                                    </div>
                                    <div class="form-group">
                                        <label for="title_color" style="display: inline-block; margin-right: 10px; vertical-align: middle;">Title Color</label>
                                        <input type="color" id="title_color" name="title_color" value="<?php echo $edit_hero['title_color'] ?? '#ffffff'; ?>" onchange="updatePreviewColors()" style="display: inline-block; vertical-align: middle; width: 50px; height: 35px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                                    </div>
                                    <div class="form-group">
                                        <label for="description_color" style="display: inline-block; margin-right: 10px; vertical-align: middle;">Description Color</label>
                                        <input type="color" id="description_color" name="description_color" value="<?php echo $edit_hero['description_color'] ?? '#ffffff'; ?>" onchange="updatePreviewColors()" style="display: inline-block; vertical-align: middle; width: 50px; height: 35px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                                    </div>
                                </div>
                            </div>

                            <!-- Button Colors -->
                            <div class="form-group">
                                <label>Button Colors</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="button_bg_color" style="display: inline-block; margin-right: 10px; vertical-align: middle;">Background</label>
                                        <input type="color" id="button_bg_color" name="button_bg_color" value="<?php echo $edit_hero['button_bg_color'] ?? '#E67E22'; ?>" onchange="updatePreviewColors()" style="display: inline-block; vertical-align: middle; width: 50px; height: 35px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                                    </div>
                                    <div class="form-group">
                                        <label for="button_text_color" style="display: inline-block; margin-right: 10px; vertical-align: middle;">Text</label>
                                        <input type="color" id="button_text_color" name="button_text_color" value="<?php echo $edit_hero['button_text_color'] ?? '#ffffff'; ?>" onchange="updatePreviewColors()" style="display: inline-block; vertical-align: middle; width: 50px; height: 35px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                                    </div>
                                    <div class="form-group">
                                        <label for="button_hover_bg_color" style="display: inline-block; margin-right: 10px; vertical-align: middle;">Hover Background</label>
                                        <input type="color" id="button_hover_bg_color" name="button_hover_bg_color" value="<?php echo $edit_hero['button_hover_bg_color'] ?? '#d35400'; ?>" onchange="updatePreviewColors()" style="display: inline-block; vertical-align: middle; width: 50px; height: 35px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                                    </div>
                                </div>
                            </div>

                            <!-- Background Controls -->
                            <div class="form-group">
                                <label>Background Settings</label>
                                <small style="display: block; margin-bottom: 10px; color: #666;">Fine-tune the background appearance and overlay opacity</small>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="background_color" style="display: inline-block; margin-right: 10px; vertical-align: middle;">Background Color</label>
                                        <input type="color" id="background_color" name="background_color" value="<?php echo $edit_hero['background_color'] ?? '#000000'; ?>" onchange="updatePreviewColors()" style="display: inline-block; vertical-align: middle; width: 50px; height: 35px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                                        <small style="display: block; margin-top: 5px;">Base color for gradients and solid backgrounds</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="background_opacity">Background Opacity</label>
                                        <input type="range" id="background_opacity" name="background_opacity" min="0" max="1" step="0.05" value="<?php echo $edit_hero['background_opacity'] ?? 0.60; ?>" onchange="updatePreviewOpacity(this.value)">
                                        <small>Opacity: <span id="opacity-value"><?php echo ($edit_hero['background_opacity'] ?? 0.60) * 100; ?>%</span></small>
                                    </div>
                                </div>
                            </div>

                            <!-- Live Preview -->
                            <div class="form-group">
                                <label>Live Preview</label>
                                <div id="hero-preview" class="hero-preview <?php echo $edit_hero['background_type'] === 'gradient' ? 'gradient-bg' : 'image-bg'; ?>" 
                                     <?php if ($edit_hero['background_type'] === 'image' && $edit_hero['background_image']): ?>
                                     style="background-image: url('<?php echo ensureAbsoluteUrl($edit_hero['background_image']); ?>');"
                                     <?php endif; ?>>
                                    <div class="hero-preview-content">
                                        <div class="caption" id="preview-caption"><?php echo htmlspecialchars($edit_hero['caption']); ?></div>
                                        <div class="title" id="preview-title"><?php echo htmlspecialchars($edit_hero['title']); ?></div>
                                        <div class="description" id="preview-description"><?php echo htmlspecialchars($edit_hero['description']); ?></div>
                                        <a href="#" class="button" id="preview-button"><?php echo htmlspecialchars($edit_hero['button_text']); ?></a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">Update Hero Section</button>
                                <a href="hero-sections.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <!-- Hero Sections List -->
                <!-- Pages Without Hero Sections -->
                <?php if (!empty($pages_without_heroes)): ?>
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2>Pages Without Hero Sections</h2>
                        <div>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="scan_pages">
                                <button type="submit" class="btn btn-secondary btn-sm">🔍 Scan for New Pages</button>
                            </form>
                        </div>
                    </div>

                    <div class="admin-card-body">
                        <p>The following pages were detected but don't have hero sections yet:</p>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Page</th>
                                        <th>File</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pages_without_heroes as $page): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($page['page_title']); ?></strong>
                                                <br><small><?php echo htmlspecialchars($page['page_name']); ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($page['file_path']); ?></td>
                                            <td>
                                                <button class="btn btn-primary btn-sm" onclick="showCreateHeroForm('<?php echo htmlspecialchars($page['page_name']); ?>', '<?php echo htmlspecialchars($page['page_title']); ?>')">
                                                    + Create Hero Section
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- All Hero Sections -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2>All Hero Sections</h2>
                        <p>Manage hero sections that appear before the footer on each page</p>
                    </div>

                    <div class="admin-card-body">
                        <?php if (empty($hero_sections)): ?>
                            <p>No hero sections found.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="admin-table">
                                    <thead>
                                        <tr>
                                            <th>Page</th>
                                            <th>Title</th>
                                            <th>Caption</th>
                                            <th>Background</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($hero_sections as $hero): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($hero['page_title']); ?></strong>
                                                    <br><small><?php echo htmlspecialchars($hero['page_name']); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($hero['title']); ?></td>
                                                <td><?php echo htmlspecialchars($hero['caption']); ?></td>
                                                <td>
                                                    <?php if ($hero['background_type'] === 'gradient'): ?>
                                                        <div style="width: 40px; height: 20px; background: <?php echo htmlspecialchars($hero['background_gradient']); ?>; border-radius: 2px; display: inline-block;"></div>
                                                        <small>Gradient</small>
                                                    <?php else: ?>
                                                        <?php if ($hero['background_image']): ?>
                                                            <img src="<?php echo ensureAbsoluteUrl($hero['background_image']); ?>" alt="Background" style="width: 40px; height: 20px; object-fit: cover; border-radius: 2px;">
                                                            <small>Image</small>
                                                        <?php else: ?>
                                                            <small>No image</small>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="status-badge <?php echo $hero['active'] ? 'active' : 'inactive'; ?>">
                                                        <?php echo $hero['active'] ? 'Active' : 'Inactive'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="hero-sections.php?edit=<?php echo $hero['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                    
                                                    <form method="POST" style="display: inline-block;" onsubmit="return confirm('Toggle status for this hero section?');">
                                                        <input type="hidden" name="action" value="toggle_active">
                                                        <input type="hidden" name="hero_id" value="<?php echo $hero['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-secondary">
                                                            <?php echo $hero['active'] ? 'Disable' : 'Enable'; ?>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Create Hero Section Modal -->
    <div id="create-hero-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
            <h3 id="modal-title">Create Hero Section</h3>
            <form method="POST" id="create-hero-form">
                <input type="hidden" name="action" value="create_hero_for_page">
                <input type="hidden" name="page_name" id="modal-page-name">
                <input type="hidden" name="page_title" id="modal-page-title">

                <div class="form-group">
                    <label for="modal-caption">Caption Text</label>
                    <input type="text" id="modal-caption" name="caption" value="Welcome" placeholder="Welcome">
                </div>

                <div class="form-group">
                    <label for="modal-title-text">Main Title</label>
                    <input type="text" id="modal-title-text" name="title" value="Ready to Get Started?" placeholder="Ready to Get Started?" required>
                </div>

                <div class="form-group">
                    <label for="modal-description">Description</label>
                    <textarea id="modal-description" name="description" rows="3" placeholder="Discover what we can do for you...">Discover what we can do for you with our professional services and expertise.</textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="modal-button-text">Button Text</label>
                        <input type="text" id="modal-button-text" name="button_text" value="Learn More" placeholder="Learn More">
                    </div>
                    <div class="form-group">
                        <label for="modal-button-link">Button Link</label>
                        <select id="modal-button-link" name="button_link" onchange="toggleModalCustomLinkInput()">
                            <option value="">Select a page...</option>
                            <?php
                            // Get all pages from site_pages table for modal
                            $db_modal = Database::getConnection();
                            $modal_pages_query = "SELECT page_name, page_title FROM site_pages ORDER BY page_title";
                            $modal_pages_result = $db_modal->query($modal_pages_query);

                            if ($modal_pages_result) {
                                while ($page = $modal_pages_result->fetch(PDO::FETCH_ASSOC)) {
                                    $selected = ($page['page_name'] === 'contact') ? 'selected' : '';
                                    echo '<option value="' . htmlspecialchars($page['page_name']) . '" ' . $selected . '>' . htmlspecialchars($page['page_title']) . '</option>';
                                }
                            }
                            ?>
                            <option value="custom">Other/Custom URL</option>
                        </select>
                        <input type="text" id="modal-custom-button-link" name="custom_button_link"
                               placeholder="Enter custom URL or page slug"
                               style="margin-top: 10px; display: none;">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="modal-height-type">Height</label>
                        <select id="modal-height-type" name="height_type">
                            <option value="small">Small (300px)</option>
                            <option value="medium" selected>Medium (400px)</option>
                            <option value="500px">500px</option>
                            <option value="large">Large (600px)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="modal-button-bg">Button Color</label>
                        <input type="color" id="modal-button-bg" name="button_bg_color" value="#E67E22">
                    </div>
                </div>

                <input type="hidden" name="caption_color" value="#ffffff">
                <input type="hidden" name="title_color" value="#ffffff">
                <input type="hidden" name="description_color" value="#ffffff">
                <input type="hidden" name="button_text_color" value="#ffffff">
                <input type="hidden" name="button_hover_bg_color" value="#d35400">
                <input type="hidden" name="background_color" value="#000000">
                <input type="hidden" name="background_opacity" value="0.60">

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Create Hero Section</button>
                    <button type="button" class="btn btn-secondary" onclick="hideCreateHeroForm()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function toggleBackgroundType() {
            const gradientOptions = document.getElementById('gradient-options');
            const imageOptions = document.getElementById('image-options');
            const preview = document.getElementById('hero-preview');
            const gradientRadio = document.querySelector('input[name="background_type"][value="gradient"]');
            
            if (gradientRadio.checked) {
                gradientOptions.style.display = 'block';
                imageOptions.style.display = 'none';
                preview.className = 'hero-preview gradient-bg';
                preview.style.backgroundImage = '';
                updatePreview();
            } else {
                gradientOptions.style.display = 'none';
                imageOptions.style.display = 'block';
                preview.className = 'hero-preview image-bg';
                updatePreview();
            }
        }
        
        function selectGradient(gradient) {
            document.getElementById('background_gradient').value = gradient;
            updatePreview();
            
            // Update selected state
            document.querySelectorAll('.gradient-preset').forEach(preset => {
                preset.classList.remove('selected');
            });
            event.target.classList.add('selected');
        }
        
        function updatePreview() {
            const preview = document.getElementById('hero-preview');
            const gradientInput = document.getElementById('background_gradient');
            const gradientRadio = document.querySelector('input[name="background_type"][value="gradient"]');
            
            if (gradientRadio.checked) {
                preview.style.setProperty('--gradient-preview', gradientInput.value);
            }
            
            // Update text content
            document.getElementById('preview-caption').textContent = document.getElementById('caption').value;
            document.getElementById('preview-title').textContent = document.getElementById('title').value;
            document.getElementById('preview-description').textContent = document.getElementById('description').value;
            document.getElementById('preview-button').textContent = document.getElementById('button_text').value;
        }
        
        // Add event listeners for live preview
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = ['caption', 'title', 'description', 'button_text', 'background_gradient'];
            inputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updatePreview);
                }
            });
            
            // Initial preview update
            updatePreview();
        });

        // New functions for enhanced styling controls
        function toggleCustomHeight() {
            const heightType = document.getElementById('height_type').value;
            const customGroup = document.getElementById('custom-height-group');
            customGroup.style.display = heightType === 'custom' ? 'block' : 'none';
            updatePreviewHeight();
        }

        function updatePreviewHeight() {
            const preview = document.getElementById('hero-preview');
            const heightType = document.getElementById('height_type').value;
            const customHeight = document.getElementById('height_custom').value;

            let height;
            switch(heightType) {
                case 'small': height = '300px'; break;
                case 'medium': height = '400px'; break;
                case '500px': height = '500px'; break;
                case 'large': height = '600px'; break;
                case 'custom': height = (customHeight || 400) + 'px'; break;
                default: height = '400px';
            }

            preview.style.minHeight = height;
        }

        function updatePreviewColors() {
            const preview = document.getElementById('hero-preview');
            const captionColor = document.getElementById('caption_color').value;
            const titleColor = document.getElementById('title_color').value;
            const descriptionColor = document.getElementById('description_color').value;
            const buttonBg = document.getElementById('button_bg_color').value;
            const buttonText = document.getElementById('button_text_color').value;

            // Update text colors
            document.getElementById('preview-caption').style.color = captionColor;
            document.getElementById('preview-title').style.color = titleColor;
            document.getElementById('preview-description').style.color = descriptionColor;

            // Update button colors
            const button = document.getElementById('preview-button');
            button.style.backgroundColor = buttonBg;
            button.style.color = buttonText;
        }

        function updatePreviewOpacity(value) {
            document.getElementById('opacity-value').textContent = Math.round(value * 100) + '%';

            // Update preview overlay opacity
            const preview = document.getElementById('hero-preview');
            const currentGradient = document.getElementById('background_gradient').value;

            // Update the gradient with new opacity
            if (currentGradient.includes('rgba')) {
                // Replace existing rgba opacity values
                const newGradient = currentGradient.replace(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/g,
                    (match, r, g, b) => `rgba(${r}, ${g}, ${b}, ${value})`);
                preview.style.setProperty('--gradient-preview', newGradient);
            }
        }

        // Add event listeners for new controls
        document.addEventListener('DOMContentLoaded', function() {
            // Height controls
            const heightType = document.getElementById('height_type');
            const heightCustom = document.getElementById('height_custom');
            if (heightType) heightType.addEventListener('change', updatePreviewHeight);
            if (heightCustom) heightCustom.addEventListener('input', updatePreviewHeight);

            // Color controls
            const colorInputs = ['caption_color', 'title_color', 'description_color', 'button_bg_color', 'button_text_color'];
            colorInputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.addEventListener('change', updatePreviewColors);
            });

            // Opacity control
            const opacitySlider = document.getElementById('background_opacity');
            if (opacitySlider) {
                opacitySlider.addEventListener('input', function() {
                    updatePreviewOpacity(this.value);
                });
            }

            // Initial updates
            updatePreviewHeight();
            updatePreviewColors();
        });

        // Modal functions
        function showCreateHeroForm(pageName, pageTitle) {
            document.getElementById('modal-page-name').value = pageName;
            document.getElementById('modal-page-title').value = pageTitle;
            document.getElementById('modal-title').textContent = 'Create Hero Section for ' + pageTitle;
            document.getElementById('create-hero-modal').style.display = 'block';
        }

        function hideCreateHeroForm() {
            document.getElementById('create-hero-modal').style.display = 'none';
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('create-hero-modal');
            if (event.target === modal) {
                hideCreateHeroForm();
            }
        });

        function toggleCustomLinkInput() {
            const select = document.getElementById('button_link');
            const customInput = document.getElementById('custom_button_link');

            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.focus();
            } else {
                customInput.style.display = 'none';
                customInput.value = '';
            }
        }

        function toggleModalCustomLinkInput() {
            const select = document.getElementById('modal-button-link');
            const customInput = document.getElementById('modal-custom-button-link');

            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.focus();
            } else {
                customInput.style.display = 'none';
                customInput.value = '';
            }
        }
    </script>
</body>
</html>

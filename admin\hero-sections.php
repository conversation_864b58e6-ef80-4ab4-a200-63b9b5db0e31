<?php
/**
 * Hero Sections Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Session is already started in functions.php

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_hero':
                try {
                    $db = Database::getConnection();
                    
                    // Handle background image upload
                    $background_image = $_POST['current_background_image'] ?? '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    }
                    
                    $stmt = $db->prepare("
                        UPDATE hero_sections SET 
                            caption = ?, 
                            title = ?, 
                            description = ?, 
                            button_text = ?, 
                            button_link = ?, 
                            background_type = ?, 
                            background_image = ?, 
                            background_gradient = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    
                    $result = $stmt->execute([
                        sanitizeInput($_POST['caption']),
                        sanitizeInput($_POST['title']),
                        sanitizeInput($_POST['description']),
                        sanitizeInput($_POST['button_text']),
                        sanitizeInput($_POST['button_link']),
                        sanitizeInput($_POST['background_type']),
                        $background_image,
                        sanitizeInput($_POST['background_gradient']),
                        intval($_POST['hero_id'])
                    ]);
                    
                    if ($result) {
                        $message = 'Hero section updated successfully!';
                    } else {
                        $error = 'Failed to update hero section.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;
                
            case 'toggle_active':
                try {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("UPDATE hero_sections SET active = NOT active WHERE id = ?");
                    $result = $stmt->execute([intval($_POST['hero_id'])]);
                    
                    if ($result) {
                        $message = 'Hero section status updated!';
                    } else {
                        $error = 'Failed to update status.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all hero sections
try {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM hero_sections ORDER BY page_name");
    $hero_sections = $stmt->fetchAll();
} catch (Exception $e) {
    $error = 'Error loading hero sections: ' . $e->getMessage();
    $hero_sections = [];
}

// Get hero for editing
$edit_hero = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $db->prepare("SELECT * FROM hero_sections WHERE id = ?");
        $stmt->execute([intval($_GET['edit'])]);
        $edit_hero = $stmt->fetch();
    } catch (Exception $e) {
        $error = 'Error loading hero section: ' . $e->getMessage();
    }
}

$page_title = 'Hero Sections Management';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <style>
        .hero-preview {
            background: #f5f5f5;
            border-radius: 8px;
            padding: 2rem;
            margin: 1rem 0;
            position: relative;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        
        .hero-preview.gradient-bg {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
        }
        
        .hero-preview.image-bg {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        
        .hero-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            z-index: 1;
        }
        
        .hero-preview.gradient-bg::before {
            background: var(--gradient-preview, linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%));
        }
        
        .hero-preview.image-bg::before {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
        }
        
        .hero-preview-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-preview .caption {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }
        
        .hero-preview .title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .hero-preview .description {
            font-size: 1rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }
        
        .hero-preview .button {
            display: inline-block;
            background: #E67E22;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .background-type-toggle {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .background-type-toggle label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }
        
        .gradient-presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .gradient-preset {
            height: 60px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .gradient-preset:hover,
        .gradient-preset.selected {
            border-color: #E67E22;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge.inactive {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><?php echo $page_title; ?></h1>
            <a href="../" class="btn btn-secondary" target="_blank">View Site</a>
        </div>
        
        <nav class="admin-nav">
            <a href="index.php">Theme Options</a>
            <a href="sliders.php">Sliders</a>
            <a href="services.php">Services</a>
            <a href="projects.php">Projects</a>
            <a href="team.php">Team</a>
            <a href="testimonials.php">Testimonials</a>
            <a href="blog.php">Blog</a>
            <a href="hero-sections.php" class="active">Hero Sections</a>
            <a href="contacts.php">Contact Submissions</a>
        </nav>
        
        <div class="admin-content">
            <?php if ($message): ?>
                <div class="message success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="message error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($edit_hero): ?>
                <!-- Edit Hero Section Form -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2>Edit Hero Section: <?php echo htmlspecialchars($edit_hero['page_title']); ?></h2>
                        <a href="hero-sections.php" class="btn btn-secondary">← Back to List</a>
                    </div>
                    
                    <div class="admin-card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="update_hero">
                            <input type="hidden" name="hero_id" value="<?php echo $edit_hero['id']; ?>">
                            <input type="hidden" name="current_background_image" value="<?php echo htmlspecialchars($edit_hero['background_image']); ?>">
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="caption">Caption Text</label>
                                    <input type="text" id="caption" name="caption" value="<?php echo htmlspecialchars($edit_hero['caption']); ?>" placeholder="Ready to Build?">
                                    <small>Small text above the main title</small>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="title">Main Title</label>
                                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($edit_hero['title']); ?>" required placeholder="Ready to Get Started?">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="description">Description Text</label>
                                    <textarea id="description" name="description" rows="3" placeholder="Let's transform your vision into reality..."><?php echo htmlspecialchars($edit_hero['description']); ?></textarea>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="button_text">Button Text</label>
                                    <input type="text" id="button_text" name="button_text" value="<?php echo htmlspecialchars($edit_hero['button_text']); ?>" placeholder="Start Your Project">
                                </div>
                                
                                <div class="form-group">
                                    <label for="button_link">Button Link</label>
                                    <input type="text" id="button_link" name="button_link" value="<?php echo htmlspecialchars($edit_hero['button_link']); ?>" placeholder="contact">
                                    <small>Page slug or full URL</small>
                                </div>
                            </div>
                            
                            <!-- Background Type Selection -->
                            <div class="form-group">
                                <label>Background Type</label>
                                <div class="background-type-toggle">
                                    <label>
                                        <input type="radio" name="background_type" value="gradient" <?php echo $edit_hero['background_type'] === 'gradient' ? 'checked' : ''; ?> onchange="toggleBackgroundType()">
                                        Gradient Background
                                    </label>
                                    <label>
                                        <input type="radio" name="background_type" value="image" <?php echo $edit_hero['background_type'] === 'image' ? 'checked' : ''; ?> onchange="toggleBackgroundType()">
                                        Image Background
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Gradient Background Options -->
                            <div id="gradient-options" style="display: <?php echo $edit_hero['background_type'] === 'gradient' ? 'block' : 'none'; ?>;">
                                <div class="form-group">
                                    <label for="background_gradient">Background Gradient</label>
                                    <input type="text" id="background_gradient" name="background_gradient" value="<?php echo htmlspecialchars($edit_hero['background_gradient']); ?>" placeholder="linear-gradient(...)">
                                    
                                    <div class="gradient-presets">
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);" onclick="selectGradient('linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)')">
                                            Dark Overlay
                                        </div>
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(230, 126, 34, 0.8) 0%, rgba(230, 126, 34, 0.4) 100%);" onclick="selectGradient('linear-gradient(135deg, rgba(230, 126, 34, 0.8) 0%, rgba(230, 126, 34, 0.4) 100%)')">
                                            Orange Accent
                                        </div>
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(45, 45, 45, 0.7) 100%);" onclick="selectGradient('linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(45, 45, 45, 0.7) 100%)')">
                                            Dark Theme
                                        </div>
                                        <div class="gradient-preset" style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.1) 100%);" onclick="selectGradient('linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.1) 100%)')">
                                            Light Overlay
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Image Background Options -->
                            <div id="image-options" style="display: <?php echo $edit_hero['background_type'] === 'image' ? 'block' : 'none'; ?>;">
                                <div class="form-group">
                                    <label for="background_image">Background Image</label>
                                    <input type="file" id="background_image" name="background_image" accept="image/*">
                                    <?php if ($edit_hero['background_image']): ?>
                                        <div style="margin-top: 1rem;">
                                            <img src="<?php echo ensureAbsoluteUrl($edit_hero['background_image']); ?>" alt="Current Background" style="max-width: 200px; height: auto; border-radius: 4px;">
                                            <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Current background image</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Live Preview -->
                            <div class="form-group">
                                <label>Live Preview</label>
                                <div id="hero-preview" class="hero-preview <?php echo $edit_hero['background_type'] === 'gradient' ? 'gradient-bg' : 'image-bg'; ?>" 
                                     <?php if ($edit_hero['background_type'] === 'image' && $edit_hero['background_image']): ?>
                                     style="background-image: url('<?php echo ensureAbsoluteUrl($edit_hero['background_image']); ?>');"
                                     <?php endif; ?>>
                                    <div class="hero-preview-content">
                                        <div class="caption" id="preview-caption"><?php echo htmlspecialchars($edit_hero['caption']); ?></div>
                                        <div class="title" id="preview-title"><?php echo htmlspecialchars($edit_hero['title']); ?></div>
                                        <div class="description" id="preview-description"><?php echo htmlspecialchars($edit_hero['description']); ?></div>
                                        <a href="#" class="button" id="preview-button"><?php echo htmlspecialchars($edit_hero['button_text']); ?></a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">Update Hero Section</button>
                                <a href="hero-sections.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <!-- Hero Sections List -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2>All Hero Sections</h2>
                        <p>Manage hero sections that appear before the footer on each page</p>
                    </div>
                    
                    <div class="admin-card-body">
                        <?php if (empty($hero_sections)): ?>
                            <p>No hero sections found.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="admin-table">
                                    <thead>
                                        <tr>
                                            <th>Page</th>
                                            <th>Title</th>
                                            <th>Caption</th>
                                            <th>Background</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($hero_sections as $hero): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($hero['page_title']); ?></strong>
                                                    <br><small><?php echo htmlspecialchars($hero['page_name']); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($hero['title']); ?></td>
                                                <td><?php echo htmlspecialchars($hero['caption']); ?></td>
                                                <td>
                                                    <?php if ($hero['background_type'] === 'gradient'): ?>
                                                        <div style="width: 40px; height: 20px; background: <?php echo htmlspecialchars($hero['background_gradient']); ?>; border-radius: 2px; display: inline-block;"></div>
                                                        <small>Gradient</small>
                                                    <?php else: ?>
                                                        <?php if ($hero['background_image']): ?>
                                                            <img src="<?php echo ensureAbsoluteUrl($hero['background_image']); ?>" alt="Background" style="width: 40px; height: 20px; object-fit: cover; border-radius: 2px;">
                                                            <small>Image</small>
                                                        <?php else: ?>
                                                            <small>No image</small>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="status-badge <?php echo $hero['active'] ? 'active' : 'inactive'; ?>">
                                                        <?php echo $hero['active'] ? 'Active' : 'Inactive'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="hero-sections.php?edit=<?php echo $hero['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                    
                                                    <form method="POST" style="display: inline-block;" onsubmit="return confirm('Toggle status for this hero section?');">
                                                        <input type="hidden" name="action" value="toggle_active">
                                                        <input type="hidden" name="hero_id" value="<?php echo $hero['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-secondary">
                                                            <?php echo $hero['active'] ? 'Disable' : 'Enable'; ?>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function toggleBackgroundType() {
            const gradientOptions = document.getElementById('gradient-options');
            const imageOptions = document.getElementById('image-options');
            const preview = document.getElementById('hero-preview');
            const gradientRadio = document.querySelector('input[name="background_type"][value="gradient"]');
            
            if (gradientRadio.checked) {
                gradientOptions.style.display = 'block';
                imageOptions.style.display = 'none';
                preview.className = 'hero-preview gradient-bg';
                preview.style.backgroundImage = '';
                updatePreview();
            } else {
                gradientOptions.style.display = 'none';
                imageOptions.style.display = 'block';
                preview.className = 'hero-preview image-bg';
                updatePreview();
            }
        }
        
        function selectGradient(gradient) {
            document.getElementById('background_gradient').value = gradient;
            updatePreview();
            
            // Update selected state
            document.querySelectorAll('.gradient-preset').forEach(preset => {
                preset.classList.remove('selected');
            });
            event.target.classList.add('selected');
        }
        
        function updatePreview() {
            const preview = document.getElementById('hero-preview');
            const gradientInput = document.getElementById('background_gradient');
            const gradientRadio = document.querySelector('input[name="background_type"][value="gradient"]');
            
            if (gradientRadio.checked) {
                preview.style.setProperty('--gradient-preview', gradientInput.value);
            }
            
            // Update text content
            document.getElementById('preview-caption').textContent = document.getElementById('caption').value;
            document.getElementById('preview-title').textContent = document.getElementById('title').value;
            document.getElementById('preview-description').textContent = document.getElementById('description').value;
            document.getElementById('preview-button').textContent = document.getElementById('button_text').value;
        }
        
        // Add event listeners for live preview
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = ['caption', 'title', 'description', 'button_text', 'background_gradient'];
            inputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updatePreview);
                }
            });
            
            // Initial preview update
            updatePreview();
        });
    </script>
</body>
</html>

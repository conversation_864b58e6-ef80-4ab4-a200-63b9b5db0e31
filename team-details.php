<?php
/**
 * Team Member Details Page
 * Individual team member profile page
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Check if team details are enabled
$enable_team_details = getThemeOption('enable_team_details', 0);
if (!$enable_team_details) {
    header('Location: team.php');
    exit;
}

// Check if this is CEO profile or regular team member
$is_ceo = isset($_GET['ceo']) && $_GET['ceo'] == '1';
$member_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$is_ceo && !$member_id) {
    header('Location: team.php');
    exit;
}

if ($is_ceo) {
    // Get CEO information from theme options
    $member = [
        'name' => getThemeOption('ceo_name', '<PERSON>'),
        'position' => getThemeOption('ceo_title', 'Chief Executive Officer & Principal Architect'),
        'bio' => getThemeOption('ceo_bio', 'With over 25 years of experience in architectural design and construction management, <PERSON> founded Monolith Design with a vision to create spaces that inspire and endure.'),
        'photo' => getThemeOption('ceo_photo', ''),
        'email' => getThemeOption('ceo_email', ''),
        'linkedin_url' => getThemeOption('ceo_linkedin', ''),
        'achievements' => getThemeOption('ceo_achievements', 'Licensed Architect in 12 states\nLEED AP BD+C Certified Professional\nAIA Gold Medal Recipient (2023)')
    ];
} else {
    // Get team member data
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM team_members WHERE id = ? AND active = 1");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if (!$member) {
            header('Location: team.php');
            exit;
        }
    } catch (Exception $e) {
        header('Location: team.php');
        exit;
    }
}

$pageTitle = htmlspecialchars($member['name']) . ' - ' . htmlspecialchars($member['position']);
$pageDescription = 'Learn more about ' . htmlspecialchars($member['name']) . ', ' . htmlspecialchars($member['position']) . ' at Monolith Design.';

// Get other team members for "Other Team Members" section
try {
    if ($is_ceo) {
        // For CEO, show regular team members
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM team_members WHERE active = 1 ORDER BY sort_order ASC, name ASC LIMIT 3");
        $stmt->execute();
        $other_members = $stmt->fetchAll();
    } else {
        // For regular team members, exclude current member
        $stmt = $db->prepare("SELECT * FROM team_members WHERE id != ? AND active = 1 ORDER BY sort_order ASC, name ASC LIMIT 3");
        $stmt->execute([$member_id]);
        $other_members = $stmt->fetchAll();
    }
} catch (Exception $e) {
    $other_members = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Team Details Page Specific CSS -->
    <style>
        /* ===== TEAM DETAILS PAGE STYLES ===== */
        
        /* Hero Section */
        .team-detail-hero {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 100%), 
                        url('https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=1600&h=900&fit=crop') center/cover;
            padding: 8rem 0;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .team-detail-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--accent-color);
            opacity: 0.1;
            z-index: 1;
        }
        
        .team-detail-hero .container {
            position: relative;
            z-index: 2;
        }
        
        .team-detail-hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .team-detail-hero .position {
            font-size: 1.5rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .breadcrumb {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .breadcrumb a:hover {
            color: white;
        }
        
        .breadcrumb span {
            color: rgba(255, 255, 255, 0.6);
        }
        
        /* Main Content */
        .team-detail-content {
            padding: 8rem 0;
        }
        
        .team-profile {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4rem;
            align-items: start;
            margin-bottom: 6rem;
        }
        
        .team-profile-image {
            position: relative;
        }
        
        .team-profile-image img {
            width: 100%;
            max-width: 400px;
            aspect-ratio: 4/5;
            object-fit: cover;
            border-radius: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .team-profile-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .team-profile-info h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .team-role {
            font-size: 1.3rem;
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 2rem;
        }
        
        .team-bio {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .team-contact {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: #f8f9fa;
            border-radius: 50px;
            text-decoration: none;
            color: var(--text-color);
            transition: all 0.3s;
        }
        
        .contact-item:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .team-expertise {
            background: #f8f9fa;
            padding: 3rem;
            border-radius: 10px;
            margin-bottom: 4rem;
        }
        
        .team-expertise h3 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: var(--text-color);
        }
        
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .expertise-item {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        
        .expertise-item:hover {
            transform: translateY(-5px);
        }
        
        .expertise-item h4 {
            color: var(--accent-color);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        /* CEO Achievements */
        .ceo-achievements-detail {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .achievement-item {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--accent-color);
            font-weight: 500;
            color: var(--text-color);
            transition: transform 0.3s;
        }

        .achievement-item:hover {
            transform: translateY(-3px);
        }

        /* CEO Additional Sections */
        .ceo-additional-sections {
            margin-top: 4rem;
        }

        .ceo-section {
            margin-bottom: 4rem;
        }

        .ceo-section h3 {
            color: var(--accent-color);
            font-size: 2rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.5rem;
        }

        .education-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .education-item {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--accent-color);
            transition: transform 0.3s;
        }

        .education-item:hover {
            transform: translateY(-5px);
        }

        .education-item h4 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
        }

        .education-item p {
            color: var(--text-color);
            margin-bottom: 1rem;
        }

        .education-item .year {
            background: var(--accent-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .philosophy-content blockquote {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            border-radius: 10px;
            border-left: 4px solid var(--accent-color);
            font-style: italic;
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: var(--text-color);
        }

        .philosophy-content p {
            font-size: 1.1rem;
            line-height: 1.7;
            color: var(--text-color);
        }

        .projects-highlight {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .project-item {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-top: 4px solid var(--accent-color);
            transition: transform 0.3s;
        }

        .project-item:hover {
            transform: translateY(-5px);
        }

        .project-item h4 {
            color: var(--accent-color);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .project-item p {
            color: var(--text-color);
            line-height: 1.6;
        }

        /* Other Team Members */
        .other-team-members {
            background: #f8f9fa;
            padding: 6rem 0;
        }
        
        .other-team-members h3 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 4rem;
            color: var(--text-color);
        }
        
        .other-members-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .other-member-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
            text-decoration: none;
            color: inherit;
        }
        
        .other-member-card:hover {
            transform: translateY(-10px);
            color: inherit;
        }
        
        .other-member-image {
            height: 250px;
            overflow: hidden;
        }
        
        .other-member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }
        
        .other-member-card:hover .other-member-image img {
            transform: scale(1.1);
        }
        
        .other-member-info {
            padding: 2rem;
            text-align: center;
        }
        
        .other-member-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }
        
        .other-member-position {
            color: var(--accent-color);
            font-weight: 500;
        }
        
        /* Back Navigation */
        .back-navigation {
            margin-bottom: 3rem;
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .back-btn:hover {
            color: var(--text-color);
            transform: translateX(-5px);
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .team-profile {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }
            
            .team-detail-hero h1 {
                font-size: 2.5rem;
            }
            
            .team-contact {
                justify-content: center;
                flex-wrap: wrap;
            }
        }
        
        @media (max-width: 768px) {
            .team-detail-hero,
            .team-detail-content,
            .other-team-members {
                padding: 4rem 0;
            }
            
            .team-detail-hero h1 {
                font-size: 2rem;
            }
            
            .expertise-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Team Detail Hero Section -->
    <section class="team-detail-hero">
        <div class="container">
            <div class="breadcrumb">
                <a href="<?php echo siteUrl(); ?>">Home</a>
                <span>›</span>
                <a href="<?php echo siteUrl('team'); ?>">Team</a>
                <span>›</span>
                <span><?php echo htmlspecialchars($member['name']); ?></span>
            </div>
            <h1><?php echo htmlspecialchars($member['name']); ?></h1>
            <div class="position"><?php echo htmlspecialchars($member['position']); ?></div>
        </div>
    </section>

    <!-- Team Detail Content -->
    <section class="team-detail-content">
        <div class="container">
            <div class="back-navigation">
                <a href="<?php echo siteUrl('team'); ?>" class="back-btn">
                    ← Back to Team
                </a>
            </div>
            
            <div class="team-profile">
                <div class="team-profile-image">
                    <?php if ($member['photo']): ?>
                        <img src="<?php echo ensureAbsoluteUrl($member['photo']); ?>" alt="<?php echo htmlspecialchars($member['name']); ?>">
                    <?php else: ?>
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop&crop=face" alt="<?php echo htmlspecialchars($member['name']); ?>">
                    <?php endif; ?>
                </div>
                
                <div class="team-profile-info">
                    <h2><?php echo htmlspecialchars($member['name']); ?></h2>
                    <div class="team-role"><?php echo htmlspecialchars($member['position']); ?></div>
                    
                    <?php if ($member['bio']): ?>
                        <div class="team-bio">
                            <?php
                            if ($is_ceo) {
                                // For CEO, show full comprehensive bio with paragraphs
                                $bio_paragraphs = explode('\n\n', $member['bio']);
                                foreach ($bio_paragraphs as $paragraph) {
                                    if (trim($paragraph)) {
                                        echo '<p>' . nl2br(htmlspecialchars(trim($paragraph))) . '</p>';
                                    }
                                }
                            } else {
                                // For regular team members, show standard bio
                                echo nl2br(htmlspecialchars($member['bio']));
                            }
                            ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="team-contact">
                        <?php if ($member['email']): ?>
                            <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="contact-item">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                </svg>
                                Email
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($member['linkedin_url']): ?>
                            <a href="<?php echo htmlspecialchars($member['linkedin_url']); ?>" target="_blank" class="contact-item">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                LinkedIn
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Expertise/Achievements Section -->
            <div class="team-expertise">
                <?php if ($is_ceo): ?>
                    <h3>Achievements & Recognition</h3>
                    <div class="ceo-achievements-detail">
                        <?php
                        $achievements = explode('\n', $member['achievements']);
                        foreach ($achievements as $achievement) {
                            if (trim($achievement)) {
                                echo '<div class="achievement-item">✓ ' . htmlspecialchars(trim($achievement)) . '</div>';
                            }
                        }
                        ?>
                    </div>

                    <!-- CEO Additional Sections -->
                    <div class="ceo-additional-sections">
                        <div class="ceo-section">
                            <h3>Education & Credentials</h3>
                            <div class="education-grid">
                                <div class="education-item">
                                    <h4>Master of Architecture</h4>
                                    <p>Massachusetts Institute of Technology (MIT)</p>
                                    <span class="year">1998</span>
                                </div>
                                <div class="education-item">
                                    <h4>Bachelor of Architecture</h4>
                                    <p>University of California, Berkeley</p>
                                    <span class="year">1996</span>
                                </div>
                                <div class="education-item">
                                    <h4>Professional Licenses</h4>
                                    <p>Licensed Architect in 12 states including CA, NY, TX, FL</p>
                                    <span class="year">Current</span>
                                </div>
                            </div>
                        </div>

                        <div class="ceo-section">
                            <h3>Leadership Philosophy</h3>
                            <div class="philosophy-content">
                                <blockquote>
                                    "Architecture is not just about creating buildings; it's about crafting experiences that inspire,
                                    spaces that endure, and environments that enhance human life. Every project we undertake is an
                                    opportunity to push the boundaries of what's possible while respecting the timeless principles
                                    of good design."
                                </blockquote>
                                <p>Alexander's leadership approach combines visionary thinking with practical execution, ensuring
                                that every project delivered by Monolith Design exceeds client expectations while contributing
                                positively to the built environment.</p>
                            </div>
                        </div>

                        <div class="ceo-section">
                            <h3>Notable Projects</h3>
                            <div class="projects-highlight">
                                <div class="project-item">
                                    <h4>Metropolitan Arts Center</h4>
                                    <p>$150M cultural complex featuring sustainable design and innovative acoustic engineering</p>
                                </div>
                                <div class="project-item">
                                    <h4>Skyline Corporate Headquarters</h4>
                                    <p>LEED Platinum certified office tower with revolutionary energy management systems</p>
                                </div>
                                <div class="project-item">
                                    <h4>Riverside Residential Complex</h4>
                                    <p>Award-winning mixed-use development integrating affordable housing with luxury amenities</p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <h3>Areas of Expertise</h3>
                    <div class="expertise-grid">
                        <div class="expertise-item">
                            <h4>Project Management</h4>
                            <p>Leading complex architectural projects from conception to completion with exceptional attention to detail and timeline management.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Sustainable Design</h4>
                            <p>Implementing eco-friendly design principles and LEED certification standards in residential and commercial projects.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Client Relations</h4>
                            <p>Building strong relationships with clients through clear communication and understanding of their unique vision and requirements.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Technical Innovation</h4>
                            <p>Utilizing cutting-edge design software and construction technologies to deliver innovative architectural solutions.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Other Team Members -->
    <?php if (!empty($other_members)): ?>
    <section class="other-team-members">
        <div class="container">
            <h3>Meet Other Team Members</h3>
            <div class="other-members-grid">
                <?php foreach ($other_members as $other_member): ?>
                    <a href="<?php echo siteUrl('team-details?id=' . $other_member['id']); ?>" class="other-member-card">
                        <div class="other-member-image">
                            <?php if ($other_member['photo']): ?>
                                <img src="<?php echo ensureAbsoluteUrl($other_member['photo']); ?>" alt="<?php echo htmlspecialchars($other_member['name']); ?>">
                            <?php else: ?>
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=500&fit=crop&crop=face" alt="<?php echo htmlspecialchars($other_member['name']); ?>">
                            <?php endif; ?>
                        </div>
                        <div class="other-member-info">
                            <div class="other-member-name"><?php echo htmlspecialchars($other_member['name']); ?></div>
                            <div class="other-member-position"><?php echo htmlspecialchars($other_member['position']); ?></div>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Hero CTA Section -->
    <?php include 'templates/hero-cta.php'; ?>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>

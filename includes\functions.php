<?php
/**
 * Core Functions and Database Connection
 */

// Prevent direct access
if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Database Connection Class
class Database {
    private static $connection = null;
    
    public static function getConnection() {
        if (self::$connection === null) {
            try {
                self::$connection = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                    DB_USER,
                    DB_PASS,
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ]
                );
            } catch (PDOException $e) {
                die("Database connection failed: " . $e->getMessage());
            }
        }
        return self::$connection;
    }
}

// Security Functions
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// URL and Routing Functions
function siteUrl($path = '') {
    return SITE_URL . '/' . ltrim($path, '/');
}

function themeUrl($path = '') {
    return THEME_PATH . '/' . ltrim($path, '/');
}

// Helper function to ensure URLs are absolute
function ensureAbsoluteUrl($url) {
    // If already absolute URL, return as is
    if (str_contains($url, 'http') || str_starts_with($url, '//')) {
        return $url;
    }

    // If starts with /, it's root relative, prepend domain
    if (str_starts_with($url, '/')) {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . $url;
    }

    // Otherwise, it's relative to site root
    return siteUrl($url);
}

function redirect($url) {
    header("Location: $url");
    exit;
}

function getCurrentUrl() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . 
           "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
}

// Template Functions
function loadTemplate($template, $data = []) {
    extract($data);
    $templatePath = __DIR__ . "/../templates/{$template}.php";
    if (file_exists($templatePath)) {
        include $templatePath;
    } else {
        echo "Template not found: {$template}";
    }
}

// Load Footer Component (New Modular System)
function loadFooter() {
    include __DIR__ . '/../components/footer/footer-loader.php';
    loadFooterComponent();
}

function getThemeOption($key, $default = '') {
    global $theme_options;
    
    // Try to get from database first
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT option_value FROM theme_options WHERE option_name = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    
    if ($result) {
        return $result['option_value'];
    }
    
    // Fallback to default theme options
    return isset($theme_options[$key]) ? $theme_options[$key] : $default;
}

function updateThemeOption($key, $value) {
    $db = Database::getConnection();
    $stmt = $db->prepare("
        INSERT INTO theme_options (option_name, option_value)
        VALUES (?, ?)
        ON DUPLICATE KEY UPDATE option_value = VALUES(option_value)
    ");
    return $stmt->execute([$key, $value]);
}

/**
 * Get hero section data for a specific page
 */
function getHeroSection($page_name) {
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM hero_sections WHERE page_name = ? AND active = 1");
        $stmt->execute([$page_name]);
        return $stmt->fetch();
    } catch (Exception $e) {
        return null;
    }
}

// Content Management Functions
function getSliders() {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM sliders WHERE active = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll();
}

function getServices($limit = null) {
    $db = Database::getConnection();
    $sql = "SELECT * FROM services WHERE active = 1 ORDER BY sort_order ASC";
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    $stmt = $db->query($sql);
    return $stmt->fetchAll();
}

function getServiceBySlug($slug) {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM services WHERE slug = ? AND active = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

function getProjects($category = null, $limit = null) {
    $db = Database::getConnection();
    $sql = "SELECT * FROM projects WHERE active = 1";
    $params = [];
    
    if ($category && $category !== 'all') {
        $sql .= " AND category = ?";
        $params[] = $category;
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

function getProjectBySlug($slug) {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM projects WHERE slug = ? AND active = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

function getTestimonials($limit = null) {
    $db = Database::getConnection();
    $sql = "SELECT * FROM testimonials WHERE active = 1 ORDER BY created_at DESC";
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    $stmt = $db->query($sql);
    return $stmt->fetchAll();
}

function getTeamMembers() {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM team_members WHERE active = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll();
}

function getBlogPosts($limit = null) {
    $db = Database::getConnection();
    $sql = "SELECT * FROM blog_posts WHERE active = 1 ORDER BY created_at DESC";
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    $stmt = $db->query($sql);
    return $stmt->fetchAll();
}

// Navigation Menu Functions
function getNavigationMenu() {
    // Try to get from database first
    $db = Database::getConnection();
    try {
        $stmt = $db->query("SELECT * FROM navigation_menu WHERE active = 1 ORDER BY sort_order ASC");
        $menu_items = $stmt->fetchAll();
        if (!empty($menu_items)) {
            return $menu_items;
        }
    } catch (Exception $e) {
        // Table doesn't exist, use default menu
    }

    // Default navigation menu
    return [
        [
            'id' => 1,
            'title' => getThemeOption('nav_home_title', 'Home'),
            'url' => '',
            'slug' => 'home',
            'sort_order' => 1,
            'active' => 1
        ],
        [
            'id' => 2,
            'title' => getThemeOption('nav_about_title', 'About'),
            'url' => 'about',
            'slug' => 'about',
            'sort_order' => 2,
            'active' => 1
        ],
        [
            'id' => 3,
            'title' => getThemeOption('nav_work_title', 'Work'),
            'url' => 'projects',
            'slug' => 'projects',
            'sort_order' => 3,
            'active' => 1
        ],
        [
            'id' => 4,
            'title' => getThemeOption('nav_team_title', 'Team'),
            'url' => 'team',
            'slug' => 'team',
            'sort_order' => 4,
            'active' => 1
        ],
        [
            'id' => 5,
            'title' => getThemeOption('nav_services_title', 'Services'),
            'url' => 'services',
            'slug' => 'services',
            'sort_order' => 5,
            'active' => 1,
            'has_dropdown' => true
        ],
        [
            'id' => 6,
            'title' => getThemeOption('nav_news_title', 'News'),
            'url' => 'blog',
            'slug' => 'blog',
            'sort_order' => 6,
            'active' => 1
        ],
        [
            'id' => 7,
            'title' => getThemeOption('nav_contact_title', 'Contact'),
            'url' => 'contact',
            'slug' => 'contact',
            'sort_order' => 7,
            'active' => 1
        ]
    ];
}

// Footer Navigation Functions
function getFooterCompanyLinks() {
    return [
        [
            'title' => getThemeOption('footer_company_about_title', 'About Us'),
            'url' => 'about'
        ],
        [
            'title' => getThemeOption('footer_company_team_title', 'Our Team'),
            'url' => 'team'
        ],
        [
            'title' => getThemeOption('footer_company_news_title', 'News'),
            'url' => 'news'
        ],
        [
            'title' => getThemeOption('footer_company_contact_title', 'Contact'),
            'url' => 'contact'
        ]
    ];
}

function getFooterServiceLinks() {
    $services = getServices(4); // Get top 4 services
    if (!empty($services)) {
        return array_map(function($service) {
            return [
                'title' => $service['title'],
                'url' => 'service/' . $service['slug']
            ];
        }, $services);
    }

    // Default services if none in database
    return [
        [
            'title' => getThemeOption('footer_service_1_title', 'Architectural Design'),
            'url' => 'service/architectural-design'
        ],
        [
            'title' => getThemeOption('footer_service_2_title', 'Structural Engineering'),
            'url' => 'service/structural-engineering'
        ],
        [
            'title' => getThemeOption('footer_service_3_title', 'Construction Management'),
            'url' => 'service/construction-management'
        ],
        [
            'title' => getThemeOption('footer_service_4_title', 'Sustainable Design'),
            'url' => 'service/sustainable-design'
        ]
    ];
}

function getFooterProjectLinks() {
    return [
        [
            'title' => getThemeOption('footer_project_all_title', 'All Projects'),
            'url' => 'projects'
        ],
        [
            'title' => getThemeOption('footer_project_featured_title', 'Featured Work'),
            'url' => 'work'
        ],
        [
            'title' => getThemeOption('footer_project_commercial_title', 'Commercial'),
            'url' => 'projects'
        ],
        [
            'title' => getThemeOption('footer_project_residential_title', 'Residential'),
            'url' => 'projects'
        ]
    ];
}

function getBlogPostBySlug($slug) {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND active = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Contact Form Handler
function handleContactForm($data) {
    $name = sanitizeInput($data['name']);
    $email = sanitizeInput($data['email']);
    $phone = sanitizeInput($data['phone']);
    $service = sanitizeInput($data['service']);
    $message = sanitizeInput($data['message']);
    
    // Validation
    if (empty($name) || empty($email) || empty($message)) {
        return ['success' => false, 'message' => 'Please fill in all required fields.'];
    }
    
    if (!validateEmail($email)) {
        return ['success' => false, 'message' => 'Please enter a valid email address.'];
    }
    
    // Save to database
    $db = Database::getConnection();
    $stmt = $db->prepare("
        INSERT INTO contact_submissions (name, email, phone, service, message, created_at) 
        VALUES (?, ?, ?, ?, ?, NOW())
    ");
    
    try {
        $stmt->execute([$name, $email, $phone, $service, $message]);
        
        // Send email notification (optional)
        $subject = "New Contact Form Submission - " . SITE_NAME;
        $body = "Name: $name\nEmail: $email\nPhone: $phone\nService: $service\nMessage: $message";
        
        // Uncomment to enable email notifications
        // mail(ADMIN_EMAIL, $subject, $body);
        
        return ['success' => true, 'message' => 'Thank you for your message. We will get back to you soon!'];
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'There was an error sending your message. Please try again.'];
    }
}

// File Upload Helper
function uploadFile($file, $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp']) {
    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $error_messages = [
            UPLOAD_ERR_INI_SIZE => 'File is larger than the upload_max_filesize directive in php.ini',
            UPLOAD_ERR_FORM_SIZE => 'File is larger than the MAX_FILE_SIZE directive in the HTML form',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
        ];
        $message = isset($error_messages[$file['error']]) ? $error_messages[$file['error']] : 'Unknown upload error: ' . $file['error'];
        return ['success' => false, 'message' => $message];
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'File is too large. Maximum size is ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB'];
    }
    
    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowed_types)) {
        return ['success' => false, 'message' => 'File type not allowed. Allowed types: ' . implode(', ', $allowed_types)];
    }
    
    // Create upload directory if it doesn't exist
    if (!file_exists(UPLOAD_PATH)) {
        if (!mkdir(UPLOAD_PATH, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }
    
    // Check if upload directory is writable
    if (!is_writable(UPLOAD_PATH)) {
        return ['success' => false, 'message' => 'Upload directory is not writable'];
    }
    
    // Generate unique filename
    $filename = time() . '_' . uniqid() . '.' . $extension;
    $filepath = UPLOAD_PATH . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // Ensure absolute URL for the uploaded file
        $file_url = UPLOAD_URL . $filename;
        // Double-check that we have an absolute URL
        if (!str_contains($file_url, 'http')) {
            $file_url = SITE_URL . '/' . ltrim($file_url, '/');
        }
        return ['success' => true, 'filename' => $filename, 'url' => $file_url];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file. Check permissions.'];
    }
}

// Utility Functions
function formatDate($date, $format = 'F j, Y') {
    return date($format, strtotime($date));
}

function truncateText($text, $length = 150, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

function createSlug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

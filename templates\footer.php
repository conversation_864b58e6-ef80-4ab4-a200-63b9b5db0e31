<?php
/**
 * Footer Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}
?>
<!-- Creative Modern Footer -->
<footer class="modern-footer">
    <div class="footer-background-pattern"></div>
    <div class="container">
        <div class="footer-content">
            <!-- Main Footer Content -->
            <div class="footer-main-grid">
                <!-- Brand Section -->
                <div class="footer-brand-section">
                    <div class="footer-logo-wrapper">
                        <a href="<?php echo siteUrl(); ?>" class="footer-logo-link">
                            <img src="<?php echo getThemeOption('footer_logo', getThemeOption('site_logo_white', themeUrl('images/logo-white.svg'))); ?>"
                                 alt="<?php echo getThemeOption('site_name', SITE_NAME); ?>" class="footer-logo">
                        </a>
                        <p class="footer-tagline">Crafting architectural excellence through innovative design and sustainable solutions.</p>
                    </div>

                    <!-- Newsletter Section -->
                    <div class="newsletter-section">
                        <h3 class="newsletter-title">Stay Updated</h3>
                        <p class="newsletter-description">Get the latest insights on architecture and design trends.</p>
                        <form class="newsletter-form">
                            <div class="newsletter-input-group">
                                <input type="email" placeholder="Enter your email address" class="newsletter-input" required>
                                <button type="submit" class="newsletter-button">
                                    <span>Subscribe</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M5 12h14M12 5l7 7-7 7"/>
                                    </svg>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="footer-nav-section">
                    <?php if (getThemeOption('footer_show_company', '1') == '1'): ?>
                    <div class="footer-nav-column">
                        <h4 class="footer-nav-title"><?php echo getThemeOption('footer_company_title', 'Company'); ?></h4>
                        <ul class="footer-nav-list">
                            <?php
                            $company_links = getFooterCompanyLinks();
                            foreach ($company_links as $link):
                            ?>
                                <li><a href="<?php echo siteUrl($link['url']); ?>" class="footer-nav-link"><?php echo htmlspecialchars($link['title']); ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <?php if (getThemeOption('footer_show_services', '1') == '1'): ?>
                    <div class="footer-nav-column">
                        <h4 class="footer-nav-title"><?php echo getThemeOption('footer_services_title', 'Services'); ?></h4>
                        <ul class="footer-nav-list">
                            <?php
                            $service_links = getFooterServiceLinks();
                            foreach ($service_links as $link):
                            ?>
                                <li><a href="<?php echo siteUrl($link['url']); ?>" class="footer-nav-link"><?php echo htmlspecialchars($link['title']); ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <?php if (getThemeOption('footer_show_projects', '1') == '1'): ?>
                    <div class="footer-nav-column">
                        <h4 class="footer-nav-title"><?php echo getThemeOption('footer_projects_title', 'Projects'); ?></h4>
                        <ul class="footer-nav-list">
                            <?php
                            $project_links = getFooterProjectLinks();
                            foreach ($project_links as $link):
                            ?>
                                <li><a href="<?php echo siteUrl($link['url']); ?>" class="footer-nav-link"><?php echo htmlspecialchars($link['title']); ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Contact Information -->
                <?php if (getThemeOption('footer_show_contact', '1') == '1'): ?>
                <div class="footer-contact-section">
                    <h4 class="footer-contact-title"><?php echo getThemeOption('footer_contact_title', 'Get in Touch'); ?></h4>

                    <div class="footer-contact-info-grid">
                        <div class="footer-contact-info-item">
                            <div class="footer-contact-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                    <circle cx="12" cy="10" r="3"/>
                                </svg>
                            </div>
                            <div class="footer-contact-details">
                                <span class="footer-contact-label">Office</span>
                                <div class="footer-contact-text">
                                    <?php echo nl2br(htmlspecialchars(getThemeOption('address', '123 Design Street\nArchitecture City, AC 12345'))); ?>
                                </div>
                            </div>
                        </div>

                        <div class="footer-contact-info-item">
                            <div class="footer-contact-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                    <polyline points="22,6 12,13 2,6"/>
                                </svg>
                            </div>
                            <div class="footer-contact-details">
                                <span class="footer-contact-label">Email</span>
                                <div class="footer-contact-links">
                                    <a href="mailto:<?php echo getThemeOption('email', '<EMAIL>'); ?>" class="footer-contact-link">
                                        <?php echo getThemeOption('email', '<EMAIL>'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="footer-contact-info-item">
                            <div class="footer-contact-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                </svg>
                            </div>
                            <div class="footer-contact-details">
                                <span class="footer-contact-label">Phone</span>
                                <div class="footer-contact-links">
                                    <a href="tel:<?php echo str_replace([' ', '(', ')', '-'], '', getThemeOption('phone_number', '+1(555)123-4567')); ?>" class="footer-contact-link">
                                        <?php echo getThemeOption('phone_number', '+****************'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="footer-copyright">
                        <p class="copyright-text">© <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
                        <div class="footer-legal-links">
                            <a href="<?php echo siteUrl('privacy'); ?>" class="footer-legal-link">Privacy Policy</a>
                            <a href="<?php echo siteUrl('terms'); ?>" class="footer-legal-link">Terms of Service</a>
                            <a href="<?php echo siteUrl('licensing'); ?>" class="footer-legal-link">Licenses</a>
                        </div>
                    </div>

                    <div class="footer-social">
                        <span class="social-label"><?php echo getThemeOption('footer_social_label', 'Follow Us'); ?></span>
                        <div class="social-links">
                            <?php if (getThemeOption('twitter_url')): ?>
                            <a href="<?php echo getThemeOption('twitter_url'); ?>" class="social-link" target="_blank" rel="noopener" aria-label="Twitter">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                            <?php endif; ?>
                            <?php if (getThemeOption('linkedin_url')): ?>
                            <a href="<?php echo getThemeOption('linkedin_url'); ?>" class="social-link" target="_blank" rel="noopener" aria-label="LinkedIn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                            <?php endif; ?>
                            <?php if (getThemeOption('instagram_url')): ?>
                            <a href="<?php echo getThemeOption('instagram_url'); ?>" class="social-link" target="_blank" rel="noopener" aria-label="Instagram">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                </svg>
                            </a>
                            <?php endif; ?>
                            <?php if (getThemeOption('facebook_url')): ?>
                            <a href="<?php echo getThemeOption('facebook_url'); ?>" class="social-link" target="_blank" rel="noopener" aria-label="Facebook">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

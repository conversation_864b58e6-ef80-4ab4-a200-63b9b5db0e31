<?php
/**
 * Team Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Session is already started in functions.php

// Check admin authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_team_member':
                $name = sanitizeInput($_POST['name']);
                $position = sanitizeInput($_POST['position']);
                $bio = sanitizeInput($_POST['bio']);
                $email = sanitizeInput($_POST['email']);
                $linkedin_url = sanitizeInput($_POST['linkedin_url']);
                $sort_order = intval($_POST['sort_order']);
                $active = isset($_POST['active']) ? 1 : 0;
                $photo = '';

                // Handle photo upload
                if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['photo'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $photo = $upload_result['url'];
                    } else {
                        $error = 'Photo upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                if (!$error) {
                    try {
                        $stmt = $db->prepare("INSERT INTO team_members (name, position, bio, email, photo, linkedin_url, sort_order, active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                        $result = $stmt->execute([$name, $position, $bio, $email, $photo, $linkedin_url, $sort_order, $active]);
                        
                        if ($result) {
                            $message = 'Team member added successfully!';
                        } else {
                            $error = 'Failed to add team member.';
                        }
                    } catch (Exception $e) {
                        $error = 'Error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_team_member':
                $id = intval($_POST['member_id']);
                $name = sanitizeInput($_POST['name']);
                $position = sanitizeInput($_POST['position']);
                $bio = sanitizeInput($_POST['bio']);
                $email = sanitizeInput($_POST['email']);
                $linkedin_url = sanitizeInput($_POST['linkedin_url']);
                $sort_order = intval($_POST['sort_order']);
                $active = isset($_POST['active']) ? 1 : 0;
                $photo = $_POST['current_photo'] ?? '';

                // Handle photo upload
                if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['photo'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $photo = $upload_result['url'];
                    } else {
                        $error = 'Photo upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                if (!$error) {
                    try {
                        $stmt = $db->prepare("UPDATE team_members SET name = ?, position = ?, bio = ?, email = ?, photo = ?, linkedin_url = ?, sort_order = ?, active = ? WHERE id = ?");
                        $result = $stmt->execute([$name, $position, $bio, $email, $photo, $linkedin_url, $sort_order, $active, $id]);
                        
                        if ($result) {
                            $message = 'Team member updated successfully!';
                        } else {
                            $error = 'Failed to update team member.';
                        }
                    } catch (Exception $e) {
                        $error = 'Error: ' . $e->getMessage();
                    }
                }
                break;

            case 'delete_team_member':
                try {
                    $id = intval($_POST['member_id']);
                    $stmt = $db->prepare("DELETE FROM team_members WHERE id = ?");
                    $result = $stmt->execute([$id]);
                    
                    if ($result) {
                        $message = 'Team member deleted successfully!';
                    } else {
                        $error = 'Failed to delete team member.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;

            case 'toggle_active':
                try {
                    $id = intval($_POST['member_id']);
                    $stmt = $db->prepare("UPDATE team_members SET active = NOT active WHERE id = ?");
                    $result = $stmt->execute([$id]);
                    
                    if ($result) {
                        $message = 'Team member status updated!';
                    } else {
                        $error = 'Failed to update status.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;

            case 'update_ceo_info':
                // For now, we'll store CEO info in theme options
                // In the future, this could be moved to a dedicated table
                try {
                    // Handle CEO photo upload
                    $ceo_photo = '';
                    if (isset($_FILES['ceo_photo']) && $_FILES['ceo_photo']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['ceo_photo'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $ceo_photo = $upload_result['url'];
                            // Store the new photo URL in theme options
                            updateThemeOption('ceo_photo', $ceo_photo);
                        }
                    }
                    
                    // Store other CEO information in theme options
                    updateThemeOption('ceo_name', sanitizeInput($_POST['ceo_name']));
                    updateThemeOption('ceo_title', sanitizeInput($_POST['ceo_title']));
                    updateThemeOption('ceo_bio', sanitizeInput($_POST['ceo_bio']));
                    updateThemeOption('ceo_achievements', sanitizeInput($_POST['ceo_achievements']));
                    
                    $message = 'CEO information updated successfully!';
                } catch (Exception $e) {
                    $error = 'Error updating CEO information: ' . $e->getMessage();
                }
                break;

            case 'update_team_settings':
                try {
                    updateThemeOption('enable_team_details', isset($_POST['enable_team_details']) ? 1 : 0);
                    $message = 'Team settings updated successfully!';
                } catch (Exception $e) {
                    $error = 'Error updating team settings: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all team members
try {
    $stmt = $db->query("SELECT * FROM team_members ORDER BY sort_order ASC, name ASC");
    $team_members = $stmt->fetchAll();
} catch (Exception $e) {
    $error = 'Error loading team members: ' . $e->getMessage();
    $team_members = [];
}

// Get CEO information from theme options
$ceo_info = [
    'name' => getThemeOption('ceo_name', 'Alexander Thompson'),
    'title' => getThemeOption('ceo_title', 'Chief Executive Officer & Principal Architect'),
    'bio' => getThemeOption('ceo_bio', 'With over 25 years of experience in architectural design and construction management, Alexander founded Monolith Design with a vision to create spaces that inspire and endure. His expertise spans commercial, residential, and institutional projects, with a particular focus on sustainable design and innovative construction methods.\n\nAlexander holds a Master\'s degree in Architecture from MIT and is a licensed architect in multiple states. He has been recognized by the American Institute of Architects for his contributions to sustainable design and has spoken at numerous international conferences on the future of architecture.'),
    'photo' => getThemeOption('ceo_photo', ''),
    'achievements' => getThemeOption('ceo_achievements', 'Licensed Architect in 12 states\nLEED AP BD+C Certified Professional\nAIA Gold Medal Recipient (2023)\nFeatured in Architectural Digest Top 100 Architects\nOver $2B in completed project value\nTEDx Speaker on Sustainable Architecture')
];

// Get member for editing
$edit_member = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $db->prepare("SELECT * FROM team_members WHERE id = ?");
        $stmt->execute([intval($_GET['edit'])]);
        $edit_member = $stmt->fetch();
    } catch (Exception $e) {
        $error = 'Error loading team member: ' . $e->getMessage();
    }
}

$page_title = 'Team Management';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ecf0f1;
            color: #2c3e50;
        }

        .admin-header {
            background: #34495e;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-nav {
            background: #2c3e50;
            padding: 0;
            display: flex;
            overflow-x: auto;
        }

        .admin-nav a {
            color: #bdc3c7;
            text-decoration: none;
            padding: 1rem 1.5rem;
            white-space: nowrap;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .admin-nav a:hover,
        .admin-nav a.active {
            background: #34495e;
            color: white;
            border-bottom-color: #3498db;
        }

        .admin-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .message {
            padding: 1rem;
            margin-bottom: 2rem;
            border-radius: 5px;
            font-weight: 500;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .admin-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .admin-card-header {
            background: #34495e;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px 10px 0 0;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-body {
            padding: 1.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }

        input[type="text"],
        input[type="email"],
        input[type="tel"],
        input[type="url"],
        input[type="number"],
        input[type="file"],
        textarea,
        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e0e0e0;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #3498db;
        }

        textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
            margin-right: 0.5rem;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .admin-table th,
        .admin-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .admin-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .admin-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .member-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        @media (max-width: 768px) {
            .admin-content {
                padding: 1rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .admin-table {
                font-size: 0.875rem;
            }

            .admin-table th,
            .admin-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Team Management</h1>
        <div>
            <a href="<?php echo siteUrl('team'); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Team Page</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="hero-sections.php">Hero Sections</a>
        <a href="services.php">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php" class="active">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message success"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="message error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($edit_member): ?>
            <!-- Edit Team Member Form -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h2>Edit Team Member: <?php echo htmlspecialchars($edit_member['name']); ?></h2>
                    <a href="team.php" class="btn btn-secondary">← Back to List</a>
                </div>
                
                <div class="admin-card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_team_member">
                        <input type="hidden" name="member_id" value="<?php echo $edit_member['id']; ?>">
                        <input type="hidden" name="current_photo" value="<?php echo htmlspecialchars($edit_member['photo']); ?>">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($edit_member['name']); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="position">Position/Title *</label>
                                <input type="text" id="position" name="position" value="<?php echo htmlspecialchars($edit_member['position']); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($edit_member['email'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="linkedin_url">LinkedIn URL</label>
                                <input type="url" id="linkedin_url" name="linkedin_url" value="<?php echo htmlspecialchars($edit_member['linkedin_url'] ?? ''); ?>" placeholder="https://linkedin.com/in/username">
                            </div>
                            
                            <div class="form-group">
                                <label for="sort_order">Display Order</label>
                                <input type="number" id="sort_order" name="sort_order" value="<?php echo $edit_member['sort_order']; ?>" min="0">
                            </div>
                            
                            <div class="form-group full-width">
                                <label for="bio">Biography</label>
                                <textarea id="bio" name="bio" placeholder="Enter team member's biography..."><?php echo htmlspecialchars($edit_member['bio']); ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="photo">Profile Photo</label>
                                <input type="file" id="photo" name="photo" accept="image/*">
                                <?php if ($edit_member['photo']): ?>
                                    <div style="margin-top: 0.5rem;">
                                        <img src="<?php echo ensureAbsoluteUrl($edit_member['photo']); ?>" alt="Current Photo" class="member-photo">
                                        <small style="display: block; margin-top: 0.5rem; color: #666;">Current photo</small>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="active" name="active" <?php echo $edit_member['active'] ? 'checked' : ''; ?>>
                                    <label for="active">Active (visible on website)</label>
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <button type="submit" class="btn btn-primary">Update Team Member</button>
                            <a href="team.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <!-- Add New Team Member Form -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h2>Add New Team Member</h2>
                </div>
                
                <div class="admin-card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="add_team_member">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" required placeholder="Enter full name">
                            </div>
                            
                            <div class="form-group">
                                <label for="position">Position/Title *</label>
                                <input type="text" id="position" name="position" required placeholder="e.g., Senior Architect">
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" placeholder="<EMAIL>">
                            </div>
                            
                            <div class="form-group">
                                <label for="linkedin_url">LinkedIn URL</label>
                                <input type="url" id="linkedin_url" name="linkedin_url" placeholder="https://linkedin.com/in/username">
                            </div>
                            
                            <div class="form-group">
                                <label for="sort_order">Display Order</label>
                                <input type="number" id="sort_order" name="sort_order" value="0" min="0">
                                <small style="display: block; margin-top: 0.25rem; color: #666;">Lower numbers appear first</small>
                            </div>
                            
                            <div class="form-group full-width">
                                <label for="bio">Biography</label>
                                <textarea id="bio" name="bio" placeholder="Enter team member's biography, experience, and background..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="photo">Profile Photo</label>
                                <input type="file" id="photo" name="photo" accept="image/*">
                                <small style="display: block; margin-top: 0.25rem; color: #666;">Recommended: 400x400px or larger, square aspect ratio</small>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="active" name="active" checked>
                                    <label for="active">Active (visible on website)</label>
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <button type="submit" class="btn btn-primary">Add Team Member</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Team Members List -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h2>Team Members (<?php echo count($team_members); ?>)</h2>
                    <p>Manage your team members and their information</p>
                </div>

                <div class="admin-card-body">
                    <?php if (empty($team_members)): ?>
                        <p>No team members found. Add your first team member above.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Photo</th>
                                        <th>Name & Position</th>
                                        <th>Contact</th>
                                        <th>Order</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($team_members as $member): ?>
                                        <tr>
                                            <td>
                                                <?php if ($member['photo']): ?>
                                                    <img src="<?php echo ensureAbsoluteUrl($member['photo']); ?>" alt="<?php echo htmlspecialchars($member['name']); ?>" class="member-photo">
                                                <?php else: ?>
                                                    <div class="member-photo" style="background: #ddd; display: flex; align-items: center; justify-content: center; color: #666;">
                                                        <?php echo strtoupper(substr($member['name'], 0, 2)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($member['name']); ?></strong>
                                                    <br><small><?php echo htmlspecialchars($member['position']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($member['email']): ?>
                                                    <div><small>📧 <?php echo htmlspecialchars($member['email']); ?></small></div>
                                                <?php endif; ?>
                                                <?php if ($member['linkedin_url']): ?>
                                                    <div><small>💼 <a href="<?php echo htmlspecialchars($member['linkedin_url']); ?>" target="_blank">LinkedIn</a></small></div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $member['sort_order']; ?></td>
                                            <td>
                                                <span class="status-badge status-<?php echo $member['active'] ? 'active' : 'inactive'; ?>">
                                                    <?php echo $member['active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="team.php?edit=<?php echo $member['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                    
                                                    <form method="POST" style="display: inline-block;" onsubmit="return confirm('Toggle status for this team member?');">
                                                        <input type="hidden" name="action" value="toggle_active">
                                                        <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-secondary">
                                                            <?php echo $member['active'] ? 'Disable' : 'Enable'; ?>
                                                        </button>
                                                    </form>
                                                    
                                                    <form method="POST" style="display: inline-block;" onsubmit="return confirm('Are you sure you want to delete this team member? This action cannot be undone.');">
                                                        <input type="hidden" name="action" value="delete_team_member">
                                                        <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- CEO Management Section -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h2>Team Settings</h2>
                <p>Configure team page features and functionality</p>
            </div>
            
            <div class="admin-card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_team_settings">
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="enable_team_details" name="enable_team_details" <?php echo getThemeOption('enable_team_details', 0) ? 'checked' : ''; ?>>
                            <label for="enable_team_details">Enable Team Details Pages</label>
                        </div>
                        <small style="display: block; margin-top: 0.5rem; color: #666;">Allow visitors to view individual team member detail pages with expanded information</small>
                    </div>
                    
                    <div style="margin-top: 2rem;">
                        <button type="submit" class="btn btn-primary">Update Settings</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- CEO Management Section -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h2>CEO Information</h2>
                <p>Manage the CEO spotlight section displayed on the team page</p>
            </div>
            
            <div class="admin-card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="update_ceo_info">
                    
                    <div class="form-group">
                        <label for="ceo_name">CEO Name</label>
                        <input type="text" id="ceo_name" name="ceo_name" value="<?php echo htmlspecialchars($ceo_info['name']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="ceo_title">CEO Title</label>
                        <input type="text" id="ceo_title" name="ceo_title" value="<?php echo htmlspecialchars($ceo_info['title']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="ceo_bio">CEO Biography</label>
                        <textarea id="ceo_bio" name="ceo_bio" rows="6" required><?php echo htmlspecialchars($ceo_info['bio']); ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="ceo_photo">CEO Photo</label>
                        <input type="file" id="ceo_photo" name="ceo_photo" accept="image/*">
                        <div style="margin-top: 1rem;">
                            <?php if ($ceo_info['photo']): ?>
                                <img src="<?php echo ensureAbsoluteUrl($ceo_info['photo']); ?>" alt="Current CEO Photo" class="member-photo">
                                <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Current CEO photo</p>
                            <?php else: ?>
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop&crop=face" alt="Default CEO Photo" class="member-photo">
                                <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Default placeholder photo</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="ceo_achievements">CEO Achievements (one per line)</label>
                        <textarea id="ceo_achievements" name="ceo_achievements" rows="6" placeholder="Licensed Architect in 12 states
LEED AP BD+C Certified Professional
AIA Gold Medal Recipient (2023)"><?php echo htmlspecialchars($ceo_info['achievements']); ?></textarea>
                        <small>Each line will become a separate achievement bullet point</small>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Update CEO Information</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>

<?php
// Define access constant and bypass security checks
define('MONOLITH_ACCESS', true);
session_start();
$_SESSION['admin_logged_in'] = true;

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/functions.php';

echo "<h1>PROJECT IMAGES DEBUG</h1><pre>";

try {
    $db = Database::getConnection();
    
    // Get all projects with their image paths
    echo "=== PROJECT IMAGE PATHS IN DATABASE ===\n";
    $stmt = $db->query("SELECT id, title, slug, featured_image, gallery FROM projects ORDER BY id");
    $projects = $stmt->fetchAll();
    
    foreach ($projects as $project) {
        echo "\n--- Project: {$project['title']} ---\n";
        echo "ID: {$project['id']}\n";
        echo "Slug: {$project['slug']}\n";
        echo "Featured Image: " . ($project['featured_image'] ?: 'NULL') . "\n";
        
        if ($project['featured_image']) {
            // Check if it's a relative or absolute path
            if (strpos($project['featured_image'], 'http') === 0) {
                echo "  → Type: Absolute URL\n";
            } elseif (strpos($project['featured_image'], '/') === 0) {
                echo "  → Type: Root-relative path\n";
            } else {
                echo "  → Type: Relative path\n";
            }
            
            // Check if file exists
            $file_path = '';
            if (strpos($project['featured_image'], 'http') === 0) {
                // For absolute URLs, convert to file path
                $file_path = str_replace(SITE_URL . '/', '', $project['featured_image']);
            } else {
                $file_path = ltrim($project['featured_image'], '/');
            }
            
            if (file_exists($file_path)) {
                echo "  → File exists: YES\n";
            } else {
                echo "  → File exists: NO (looking for: $file_path)\n";
            }
        }
        
        // Check gallery images
        if ($project['gallery']) {
            $gallery = json_decode($project['gallery'], true);
            if ($gallery) {
                echo "Gallery Images: " . count($gallery) . " images\n";
                foreach ($gallery as $i => $img) {
                    $img_url = is_array($img) ? $img['url'] : $img;
                    echo "  Gallery[$i]: $img_url\n";
                }
            }
        } else {
            echo "Gallery: NULL\n";
        }
    }
    
    echo "\n\n=== UPLOAD CONFIGURATION ===\n";
    echo "UPLOAD_PATH: " . UPLOAD_PATH . "\n";
    echo "UPLOAD_URL: " . UPLOAD_URL . "\n";
    echo "SITE_URL: " . SITE_URL . "\n";
    
    echo "\n=== UPLOAD DIRECTORY CHECK ===\n";
    if (file_exists(UPLOAD_PATH)) {
        echo "Upload directory exists: YES\n";
        echo "Upload directory writable: " . (is_writable(UPLOAD_PATH) ? 'YES' : 'NO') . "\n";
        
        // List files in upload directory
        $files = scandir(UPLOAD_PATH);
        $image_files = array_filter($files, function($file) {
            return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif|webp|svg)$/i', $file);
        });
        
        echo "Image files in upload directory: " . count($image_files) . "\n";
        foreach ($image_files as $file) {
            echo "  - $file\n";
        }
    } else {
        echo "Upload directory exists: NO\n";
    }
    
    echo "\n=== TESTING ensureAbsoluteUrl FUNCTION ===\n";
    $test_paths = [
        'assets/images/uploads/test.jpg',
        '/assets/images/uploads/test.jpg',
        'http://localhost/monolith-design/assets/images/uploads/test.jpg',
        UPLOAD_URL . 'test.jpg'
    ];
    
    foreach ($test_paths as $path) {
        echo "Input: $path\n";
        echo "Output: " . ensureAbsoluteUrl($path) . "\n\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>

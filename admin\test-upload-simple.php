<?php
/**
 * Simple Upload Test for Admin
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

session_start();
$_SESSION['admin_logged_in'] = true;

echo "<h1>SIMPLE UPLOAD TEST</h1>";

if ($_POST && isset($_FILES['test_image'])) {
    echo "<h2>Upload Result</h2>";
    echo "<pre>";
    
    echo "File details:\n";
    echo "Name: " . $_FILES['test_image']['name'] . "\n";
    echo "Size: " . $_FILES['test_image']['size'] . " bytes\n";
    echo "Type: " . $_FILES['test_image']['type'] . "\n";
    echo "Error: " . $_FILES['test_image']['error'] . "\n";
    echo "Temp: " . $_FILES['test_image']['tmp_name'] . "\n";
    
    echo "\nUpload attempt...\n";
    $result = uploadFile($_FILES['test_image'], ['jpg', 'jpeg', 'png', 'webp', 'gif']);
    
    if ($result['success']) {
        echo "✅ SUCCESS!\n";
        echo "Filename: " . $result['filename'] . "\n";
        echo "URL: " . $result['url'] . "\n";
        echo "Absolute URL: " . ensureAbsoluteUrl($result['url']) . "\n";
        
        // Show the uploaded image
        echo "</pre>";
        echo "<h3>Uploaded Image:</h3>";
        echo '<img src="' . htmlspecialchars($result['url']) . '" alt="Uploaded test image" style="max-width: 200px; border: 1px solid #ccc;">';
        echo "<pre>";
        
    } else {
        echo "❌ FAILED: " . $result['message'] . "\n";
    }
    
    echo "</pre>";
}

echo "<h2>Test Upload Form</h2>";
echo '<form method="POST" enctype="multipart/form-data">';
echo '<input type="hidden" name="MAX_FILE_SIZE" value="' . MAX_FILE_SIZE . '">';
echo '<input type="file" name="test_image" accept="image/*" required>';
echo '<button type="submit">Upload Test Image</button>';
echo '</form>';

echo "<h2>Current Configuration</h2>";
echo "<pre>";
echo "UPLOAD_PATH: " . UPLOAD_PATH . "\n";
echo "UPLOAD_URL: " . UPLOAD_URL . "\n";
echo "SITE_URL: " . SITE_URL . "\n";
echo "</pre>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
form { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
input, button { margin: 5px; padding: 8px; }
button { background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
</style>

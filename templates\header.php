<?php
/**
 * Header Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}
?>
<!-- Arkify-style Header -->
<header class="arkify-header" id="header">
    <div class="container">
        <div class="header-wrap">
            <!-- Logo -->
            <div class="header-logo">
                <a href="<?php echo siteUrl(); ?>" class="logo-link">
                    <img src="<?php echo ensureAbsoluteUrl(getThemeOption('site_logo', themeUrl('images/logo.svg'))); ?>"
                         alt="<?php echo getThemeOption('site_name', SITE_NAME); ?>" class="main-logo">
                </a>
            </div>

            <!-- Navigation -->
            <div class="header-nav">
                <nav class="main-navigation">
                    <div class="nav-list">
                        <?php
                        $navigation_menu = getNavigationMenu();
                        $current_url = getCurrentUrl();
                        foreach ($navigation_menu as $menu_item):
                            $menu_url = siteUrl($menu_item['url']);
                            $is_active = false;

                            // Determine if menu item is active
                            if (empty($menu_item['url']) && $current_url === siteUrl()) {
                                $is_active = true; // Home page
                            } elseif (!empty($menu_item['url']) && strpos($current_url, $menu_item['slug']) !== false) {
                                $is_active = true;
                            }

                            $active_class = $is_active ? 'active' : '';
                        ?>
                            <?php if (isset($menu_item['has_dropdown']) && $menu_item['has_dropdown']): ?>
                                <a href="<?php echo $menu_url; ?>" class="nav-link <?php echo $active_class; ?>"
                                   onclick="toggleServiceMenu(event)">
                                    <?php echo htmlspecialchars($menu_item['title']); ?>
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-left: 4px;">
                                        <path d="M6 9l6 6 6-6"/>
                                    </svg>
                                </a>
                            <?php else: ?>
                                <a href="<?php echo $menu_url; ?>" class="nav-link <?php echo $active_class; ?>">
                                    <?php echo htmlspecialchars($menu_item['title']); ?>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </nav>

                <!-- CTA Button -->
                <a href="<?php echo siteUrl(getThemeOption('header_cta_url', 'contact')); ?>" class="header-cta-button">
                    <div><?php echo strtoupper(getThemeOption('header_cta_text', 'GET IN TOUCH')); ?></div>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </a>
            </div>

            <!-- Mobile Menu Toggle -->
            <div class="mobile-menu-toggle" id="mobile-toggle">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </div>
        </div>
    </div>

    <!-- Service Offcanvas Menu -->
    <div class="service-offcanvas" id="service-offcanvas">
        <div class="offcanvas-overlay" onclick="closeServiceMenu()"></div>
        <div class="offcanvas-content">
            <div class="offcanvas-header">
                <h3><?php echo getThemeOption('services_menu_title', 'Our Services'); ?></h3>
                <button onclick="closeServiceMenu()" class="close-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 6L6 18M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="offcanvas-body">
                <div class="services-menu-grid">
                    <?php 
                    $services = getServices();
                    if (!empty($services)): 
                        foreach ($services as $service): 
                    ?>
                        <a href="service-details?service=<?php echo urlencode($service['slug']); ?>" class="service-menu-item">
                            <div class="service-menu-icon">
                                <?php
                                // Use icon from database, fallback to architecture.svg if not set
                                $icon_path = !empty($service['icon']) ? $service['icon'] : 'assets/images/icons/architecture.svg';

                                // If the icon path doesn't include the full path, assume it's just the filename
                                if (!str_contains($icon_path, '/')) {
                                    $icon_path = 'images/icons/' . $icon_path;
                                } else {
                                    // Remove 'assets/' prefix if present since themeUrl already handles it
                                    $icon_path = str_replace('assets/', '', $icon_path);
                                }
                                ?>
                                <img src="<?php echo themeUrl($icon_path); ?>" alt="<?php echo htmlspecialchars($service['title']); ?>" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4><?php echo htmlspecialchars($service['title']); ?></h4>
                                <p><?php echo htmlspecialchars(substr($service['description'], 0, 60) . '...'); ?></p>
                            </div>
                        </a>
                    <?php 
                        endforeach; 
                    else: 
                    ?>
                        <!-- Default services if none in database -->
                        <a href="service-details?service=architectural-design" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/architecture.svg'); ?>" alt="Architectural Design" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Architectural Design</h4>
                                <p>Creative and functional design solutions for projects...</p>
                            </div>
                        </a>
                        <a href="service-details?service=structural-engineering" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/engineering.svg'); ?>" alt="Structural Engineering" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Structural Engineering</h4>
                                <p>Expert structural analysis and engineering solutions...</p>
                            </div>
                        </a>
                        <a href="service-details?service=construction-management" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/construction.svg'); ?>" alt="Construction Management" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Construction Management</h4>
                                <p>Complete project management from start to finish...</p>
                            </div>
                        </a>
                        <a href="service-details?service=sustainable-design" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/sustainable.svg'); ?>" alt="Sustainable Design" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Sustainable Design</h4>
                                <p>Environmentally conscious design solutions...</p>
                            </div>
                        </a>
                        <a href="service-details?service=interior-design" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/interior.svg'); ?>" alt="Interior Design" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Interior Design</h4>
                                <p>Beautiful and functional interior spaces...</p>
                            </div>
                        </a>
                        <a href="service-details?service=project-management" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/project-management.svg'); ?>" alt="Project Management" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Project Management</h4>
                                <p>Professional project coordination and oversight...</p>
                            </div>
                        </a>
                        <a href="service-details?service=urban-planning" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/urban-planning.svg'); ?>" alt="Urban Planning" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Urban Planning</h4>
                                <p>Strategic urban development and planning...</p>
                            </div>
                        </a>
                        <a href="service-details?service=renovation-restoration" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/renovation.svg'); ?>" alt="Renovation & Restoration" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Renovation & Restoration</h4>
                                <p>Expert renovation and restoration services...</p>
                            </div>
                        </a>
                        <a href="service-details?service=consulting-advisory" class="service-menu-item">
                            <div class="service-menu-icon">
                                <img src="<?php echo themeUrl('images/icons/consulting.svg'); ?>" alt="Consulting & Advisory" width="24" height="24">
                            </div>
                            <div class="service-menu-content">
                                <h4>Consulting & Advisory</h4>
                                <p>Professional consulting and advisory services...</p>
                            </div>
                        </a>
                    <?php endif; ?>
                </div>
                
                <div class="offcanvas-footer">
                    <a href="<?php echo siteUrl('services'); ?>" class="view-all-services">
                        View All Services
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M7 17L17 7M17 7H7M17 7V17"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <div class="mobile-navigation" id="mobile-nav">
        <div class="mobile-nav-content">
            <div class="mobile-nav-menu">
                <?php
                $mobile_navigation = getNavigationMenu();
                foreach ($mobile_navigation as $mobile_item):
                    $mobile_url = siteUrl($mobile_item['url']);
                ?>
                    <a href="<?php echo $mobile_url; ?>" class="mobile-nav-link">
                        <?php echo htmlspecialchars($mobile_item['title']); ?>
                    </a>
                <?php endforeach; ?>
                <a href="<?php echo siteUrl(getThemeOption('header_cta_url', 'contact')); ?>" class="mobile-cta-button">
                    <div><?php echo strtoupper(getThemeOption('header_cta_text', 'GET IN TOUCH')); ?></div>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</header>

<?php
/**
 * Debug Upload Functionality in Admin Context
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Start session for admin authentication
session_start();

// Set admin session for testing
$_SESSION['admin_logged_in'] = true;

echo "<h1>ADMIN UPLOAD DEBUG</h1>";

// Check upload configuration
echo "<h2>Upload Configuration</h2>";
echo "<pre>";
echo "UPLOAD_PATH: " . UPLOAD_PATH . "\n";
echo "UPLOAD_URL: " . UPLOAD_URL . "\n";
echo "MAX_FILE_SIZE: " . MAX_FILE_SIZE . " bytes (" . (MAX_FILE_SIZE / 1024 / 1024) . " MB)\n";
echo "Current working directory: " . getcwd() . "\n";
echo "Script directory: " . __DIR__ . "\n";
echo "</pre>";

// Check upload directory
echo "<h2>Upload Directory Check</h2>";
echo "<pre>";
if (file_exists(UPLOAD_PATH)) {
    echo "✅ Upload directory exists: " . UPLOAD_PATH . "\n";
    echo "✅ Directory is writable: " . (is_writable(UPLOAD_PATH) ? 'YES' : 'NO') . "\n";
    
    // List existing files
    $files = scandir(UPLOAD_PATH);
    $image_files = array_filter($files, function($file) {
        return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif|webp|svg)$/i', $file);
    });
    echo "Existing image files: " . count($image_files) . "\n";
    foreach ($image_files as $file) {
        echo "  - $file\n";
    }
} else {
    echo "❌ Upload directory does not exist: " . UPLOAD_PATH . "\n";
    echo "Attempting to create directory...\n";
    if (mkdir(UPLOAD_PATH, 0755, true)) {
        echo "✅ Directory created successfully\n";
    } else {
        echo "❌ Failed to create directory\n";
    }
}
echo "</pre>";

// Test upload if file is submitted
if ($_POST && isset($_FILES['test_file'])) {
    echo "<h2>Upload Test Result</h2>";
    echo "<pre>";
    
    echo "=== FILE DETAILS ===\n";
    echo "Original name: " . $_FILES['test_file']['name'] . "\n";
    echo "Size: " . $_FILES['test_file']['size'] . " bytes\n";
    echo "Type: " . $_FILES['test_file']['type'] . "\n";
    echo "Error code: " . $_FILES['test_file']['error'] . "\n";
    echo "Temp name: " . $_FILES['test_file']['tmp_name'] . "\n";
    
    echo "\n=== UPLOAD ATTEMPT ===\n";
    $result = uploadFile($_FILES['test_file'], ['jpg', 'jpeg', 'png', 'webp', 'gif']);
    
    if ($result['success']) {
        echo "✅ Upload successful!\n";
        echo "Filename: " . $result['filename'] . "\n";
        echo "URL: " . $result['url'] . "\n";
        
        // Verify file exists
        $file_path = UPLOAD_PATH . $result['filename'];
        if (file_exists($file_path)) {
            echo "✅ File exists on disk: $file_path\n";
            echo "File size on disk: " . filesize($file_path) . " bytes\n";
        } else {
            echo "❌ File not found on disk: $file_path\n";
        }
        
        // Test ensureAbsoluteUrl function
        echo "\n=== URL PROCESSING ===\n";
        echo "Original URL: " . $result['url'] . "\n";
        echo "ensureAbsoluteUrl: " . ensureAbsoluteUrl($result['url']) . "\n";
        
    } else {
        echo "❌ Upload failed: " . $result['message'] . "\n";
    }
    
    echo "</pre>";
}

// Check PHP upload settings
echo "<h2>PHP Upload Settings</h2>";
echo "<pre>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "file_uploads: " . (ini_get('file_uploads') ? 'ON' : 'OFF') . "\n";
echo "upload_tmp_dir: " . (ini_get('upload_tmp_dir') ?: 'default') . "\n";
echo "</pre>";

// Test form
echo "<h2>Upload Test Form</h2>";
echo '<form method="POST" enctype="multipart/form-data" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">';
echo '<input type="hidden" name="MAX_FILE_SIZE" value="' . MAX_FILE_SIZE . '">';
echo '<label for="test_file">Select Image File:</label><br>';
echo '<input type="file" id="test_file" name="test_file" accept="image/*" required><br><br>';
echo '<button type="submit">Test Upload</button>';
echo '</form>';

// Test database connection
echo "<h2>Database Connection Test</h2>";
echo "<pre>";
try {
    $db = Database::getConnection();
    echo "✅ Database connection successful\n";
    
    // Test projects table
    $stmt = $db->query("SELECT COUNT(*) as count FROM projects");
    $result = $stmt->fetch();
    echo "✅ Projects table accessible, " . $result['count'] . " projects found\n";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
echo "</pre>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
form { background: #fff; }
input[type="file"] { margin: 10px 0; }
button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
button:hover { background: #005a87; }
</style>
